from PyQt5.QtWidgets import QDockWidget, QWidget, QVBoxLayout
from ui.Process.widget_basic_optimizers import WidgetBasicOptimizers
from ui.Process.widget_external_tools import WidgetExternalTools
from ui.Process.widget_legacy_optimizers import WidgetLegacyOptimizers

class ProcessWidget(QDockWidget):
    def __init__(self, parent=None):
        super().__init__('算法工具箱', parent)
        self.setMinimumWidth(150)
        self.setMaximumWidth(500)
        container = QWidget()
        self.layout = QVBoxLayout(container)

        # 设置紧凑的布局
        self.layout.setSpacing(4)  # 减少间距
        self.layout.setContentsMargins(5, 5, 5, 5)  # 减少边距

        self.setWidget(container)
        self.algorithms = {
            # 新的分组优化器
            'basic_optimizers': WidgetBasicOptimizers(),
            'external_tools': WidgetExternalTools(),
            'legacy_optimizers': WidgetLegacyOptimizers(),
        }
        self.current_widget = None
        self.layout.addStretch()

    def show_algorithm(self, name):
        if self.current_widget:
            self.layout.removeWidget(self.current_widget)
            self.current_widget.hide()
        widget = self.algorithms.get(name)
        if widget:
            self.layout.insertWidget(0, widget)
            widget.show()
            self.current_widget = widget
        self.show()
