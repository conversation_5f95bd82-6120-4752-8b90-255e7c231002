"""
Shader编译结果数据分析弹窗
包含原来的CompilerHelpDialog和新的ShaderAnalysisDialog
"""
import re
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                           QDialogButtonBox, QTabWidget, QWidget, QLabel,
                           QScrollArea, QFrame, QSplitter, QTableWidget,
                           QTableWidgetItem, QHeaderView, QProgressBar,
                           QGroupBox, QGridLayout, QPushButton)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QPen
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


class CompilerHelpDialog(QDialog):
    """编译器参数帮助弹窗"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('HLSL编译参数说明')
        self.setModal(True)
        self.resize(700, 600)

        layout = QVBoxLayout(self)

        # 创建文本显示区域
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.setHtml(self.get_help_content())
        layout.addWidget(self.text_edit)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

    def get_help_content(self):
        """获取帮助内容"""
        return """
        <h2>HLSL编译参数说明</h2>

        <div style="background-color: #ffebee; border: 2px solid #f44336; border-radius: 4px; padding: 8px; margin: 8px 0;">
        <b>⚠️ 重要提醒：编译器类型决定参数格式！</b><br>
        • <b>DXC内核编译器</b> 使用: <code>-T ps_6_0 -E main</code><br>
        • <b>RGA 2.9.0</b> 使用: <code>--hlsl --profile ps --entry main</code><br>
        <b>大部分RGA编译器都使用DXC内核，只有RGA 2.9.0例外！</b>
        </div>

        <h3>🎯 编译器分类：</h3>
        <ul>
        <li><b>DXC (DirectX Shader Compiler)</b> - 微软官方HLSL编译器</li>
        <li><b>RGA (DXC内核)</b> - 大部分RGA版本，使用DXC参数格式</li>
        <li><b>RGA 2.9.0</b> - 特殊版本，使用自己的参数格式</li>
        </ul>

        <h3>📝 DXC格式参数（适用于DXC和大部分RGA）：</h3>
        <ul>
        <li><b>-T &lt;profile&gt;</b> - 指定着色器类型和版本
            <ul>
            <li><code>vs_6_0</code> - 顶点着色器 6.0</li>
            <li><code>ps_6_0</code> - 像素着色器 6.0</li>
            <li><code>cs_6_0</code> - 计算着色器 6.0</li>
            <li><code>gs_6_0</code> - 几何着色器 6.0</li>
            <li><code>hs_6_0</code> - 外壳着色器 6.0</li>
            <li><code>ds_6_0</code> - 域着色器 6.0</li>
            </ul>
        </li>
        <li><b>-E &lt;entry&gt;</b> - 指定入口函数名
            <ul>
            <li><code>main</code> - 默认入口函数</li>
            <li><code>vsmain</code> - 顶点着色器入口</li>
            <li><code>psmain</code> - 像素着色器入口</li>
            </ul>
        </li>
        <li><b>优化选项：</b>
            <ul>
            <li><code>-O0</code> - 禁用优化</li>
            <li><code>-O1</code> - 基础优化</li>
            <li><code>-O2</code> - 标准优化</li>
            <li><code>-O3</code> - 激进优化</li>
            </ul>
        </li>
        <li><b>调试选项：</b>
            <ul>
            <li><code>-Zi</code> - 生成调试信息</li>
            <li><code>-Qembed_debug</code> - 嵌入调试信息</li>
            </ul>
        </li>
        <li><b>其他选项：</b>
            <ul>
            <li><code>-WX</code> - 将警告视为错误</li>
            <li><code>-all_resources_bound</code> - 假设所有资源已绑定</li>
            <li><code>-enable-16bit-types</code> - 启用16位类型</li>
            </ul>
        </li>
        </ul>

        <h3>🔧 RGA 2.9.0 特殊参数：</h3>
        <ul>
        <li><b>--hlsl</b> - 指定输入为HLSL格式（必需）</li>
        <li><b>--profile &lt;type&gt;</b> - 着色器类型
            <ul>
            <li><code>vs</code> - 顶点着色器</li>
            <li><code>ps</code> - 像素着色器</li>
            <li><code>cs</code> - 计算着色器</li>
            </ul>
        </li>
        <li><b>--entry &lt;name&gt;</b> - 入口函数名</li>
        <li><b>--asic &lt;gpu&gt;</b> - 目标GPU（可选）
            <ul>
            <li><code>gfx1030</code> - RDNA2 (RX 6000系列)</li>
            <li><code>gfx1100</code> - RDNA3 (RX 7000系列)</li>
            </ul>
        </li>
        </ul>

        <h3>💡 常用示例：</h3>
        <h4>DXC和大部分RGA：</h4>
        <ul>
        <li>顶点着色器：<code>-T vs_6_0 -E main</code></li>
        <li>像素着色器：<code>-T ps_6_0 -E main -O2</code></li>
        <li>计算着色器：<code>-T cs_6_0 -E CSMain</code></li>
        </ul>

        <h4>RGA 2.9.0：</h4>
        <ul>
        <li>像素着色器：<code>--hlsl --profile ps --entry main</code></li>
        <li>顶点着色器：<code>--hlsl --profile vs --entry main</code></li>
        <li>指定GPU：<code>--hlsl --profile ps --entry main --asic gfx1030</code></li>
        </ul>

        <h3>📊 快速参考表：</h3>
        <table border="1" style="border-collapse: collapse; margin: 8px 0;">
        <tr style="background-color: #e6f3ff;">
        <th style="padding: 4px 8px;">编译器类型</th>
        <th style="padding: 4px 8px;">像素着色器</th>
        <th style="padding: 4px 8px;">顶点着色器</th>
        <th style="padding: 4px 8px;">计算着色器</th>
        </tr>
        <tr>
        <td style="padding: 4px 8px;"><b>DXC / RGA(DXC内核)</b></td>
        <td style="padding: 4px 8px;"><code>-T ps_6_0 -E main</code></td>
        <td style="padding: 4px 8px;"><code>-T vs_6_0 -E main</code></td>
        <td style="padding: 4px 8px;"><code>-T cs_6_0 -E main</code></td>
        </tr>
        <tr style="background-color: #f9f9f9;">
        <td style="padding: 4px 8px;"><b>RGA 2.9.0</b></td>
        <td style="padding: 4px 8px;"><code>--hlsl --profile ps --entry main</code></td>
        <td style="padding: 4px 8px;"><code>--hlsl --profile vs --entry main</code></td>
        <td style="padding: 4px 8px;"><code>--hlsl --profile cs --entry main</code></td>
        </tr>
        </table>

        <h3>🚨 常见问题：</h3>
        <ul>
        <li>只有 <b>RGA 2.9.0</b> 不支持DXC格式参数，必须使用 <code>--hlsl</code></li>
        <li>如果看到 "unknown extension" 错误，说明在RGA 2.9.0中使用了DXC参数</li>
        <li>如果看到 "Unknown argument" 错误，说明在DXC中使用了RGA参数</li>
        <li>程序会根据编译器版本自动建议合适的参数格式</li>
        </ul>
        """


class ShaderFormatDetector:
    """Shader格式检测器"""
    
    @staticmethod
    def detect_format(content):
        """检测shader编译结果的格式"""
        content_lower = content.lower()
        
        # 检测DXIL格式
        if any(keyword in content_lower for keyword in ['dxil', 'dx.op', 'dx.types', '!dx.', 'target datalayout']):
            return 'DXIL'

        # 检测ISA格式 (AMD GPU汇编) - 使用更特定的关键词
        if any(keyword in content_lower for keyword in ['s_load_dwordx', 'v_interp_', 'image_sample', 'gfx10', 'asic(gfx', 's_waitcnt', 's_endpgm']):
            return 'ISA'

        # 检测HLSL汇编格式 - 使用更特定的关键词
        if any(keyword in content_lower for keyword in ['dcl_globalflags', 'dcl_sampler', 'dcl_resource_texture', 'dcl_input_ps', 'dcl_output', 'dcl_temps']):
            return 'HLSL_ASM'

        # 检测SPIR-V格式
        if any(keyword in content_lower for keyword in ['spirv', 'opcode', 'opname', 'opdecorate', '%']):
            return 'SPIRV'
        
        # 检测二进制数据
        if re.search(r'[0-9a-fA-F]{8}:\s+([0-9a-fA-F]{2}\s+){8,}', content):
            return 'BINARY'
        
        # 默认为文本格式
        return 'TEXT'


class ShaderAnalysisWorker(QThread):
    """Shader分析工作线程"""
    analysis_complete = pyqtSignal(dict)
    progress_update = pyqtSignal(int, str)
    
    def __init__(self, content, format_type):
        super().__init__()
        self.content = content
        self.format_type = format_type
    
    def run(self):
        """执行分析"""
        try:
            self.progress_update.emit(10, "检测格式...")
            
            analysis_result = {
                'format': self.format_type,
                'basic_info': self._analyze_basic_info(),
                'statistics': self._analyze_statistics(),
                'instructions': self._analyze_instructions(),
                'resources': self._analyze_resources(),
                'performance': self._analyze_performance()
            }
            
            self.progress_update.emit(100, "分析完成")
            self.analysis_complete.emit(analysis_result)
            
        except Exception as e:
            self.analysis_complete.emit({'error': str(e)})
    
    def _analyze_basic_info(self):
        """分析基本信息"""
        self.progress_update.emit(20, "分析基本信息...")
        
        info = {
            'format': self.format_type,
            'size': len(self.content),
            'lines': len(self.content.splitlines()),
            'encoding': 'UTF-8' if self.content.isascii() else 'Mixed'
        }
        
        return info
    
    def _analyze_statistics(self):
        """分析统计信息"""
        self.progress_update.emit(40, "分析统计信息...")
        
        lines = self.content.splitlines()
        stats = {
            'total_lines': len(lines),
            'non_empty_lines': len([line for line in lines if line.strip()]),
            'comment_lines': len([line for line in lines if line.strip().startswith(';') or line.strip().startswith('//')]),
            'instruction_lines': 0
        }
        
        if self.format_type in ['DXIL', 'ISA', 'HLSL_ASM']:
            stats['instruction_lines'] = stats['non_empty_lines'] - stats['comment_lines']
        
        return stats
    
    def _analyze_instructions(self):
        """分析指令"""
        self.progress_update.emit(60, "分析指令...")
        
        instructions = []
        
        if self.format_type == 'DXIL':
            instructions = self._analyze_dxil_instructions()
        elif self.format_type == 'ISA':
            instructions = self._analyze_isa_instructions()
        elif self.format_type == 'HLSL_ASM':
            instructions = self._analyze_hlsl_asm_instructions()
        
        return instructions
    
    def _analyze_dxil_instructions(self):
        """分析DXIL指令"""
        instructions = []
        lines = self.content.splitlines()
        
        for line in lines:
            line = line.strip()
            if 'call' in line and 'dx.op' in line:
                # 提取DXIL操作
                match = re.search(r'dx\.op\.(\w+)', line)
                if match:
                    instructions.append({
                        'type': 'DXIL_OP',
                        'name': match.group(1),
                        'line': line
                    })
        
        return instructions
    
    def _analyze_isa_instructions(self):
        """分析ISA指令"""
        instructions = []
        lines = self.content.splitlines()
        
        for line in lines:
            line = line.strip()
            # 匹配AMD ISA指令格式
            if re.match(r'^[sv]_\w+', line):
                parts = line.split()
                if parts:
                    instructions.append({
                        'type': 'ISA',
                        'name': parts[0],
                        'line': line
                    })
        
        return instructions
    
    def _analyze_hlsl_asm_instructions(self):
        """分析HLSL汇编指令"""
        instructions = []
        lines = self.content.splitlines()
        
        for line in lines:
            line = line.strip()
            # 匹配HLSL汇编指令
            if re.match(r'^(mov|add|mul|mad|dp3|dp4|dcl_|tex)', line):
                parts = line.split()
                if parts:
                    instructions.append({
                        'type': 'HLSL_ASM',
                        'name': parts[0],
                        'line': line
                    })
        
        return instructions
    
    def _analyze_resources(self):
        """分析资源使用"""
        self.progress_update.emit(80, "分析资源...")
        
        resources = {
            'textures': [],
            'buffers': [],
            'samplers': [],
            'constants': []
        }
        
        # 根据格式分析资源
        if self.format_type == 'DXIL':
            resources.update(self._analyze_dxil_resources())
        elif self.format_type == 'ISA':
            resources.update(self._analyze_isa_resources())
        
        return resources
    
    def _analyze_dxil_resources(self):
        """分析DXIL资源"""
        resources = {'textures': [], 'buffers': [], 'samplers': [], 'constants': []}
        
        # 查找纹理资源
        texture_matches = re.findall(r'%(\w+)\s*=.*texture', self.content, re.IGNORECASE)
        resources['textures'] = list(set(texture_matches))
        
        # 查找缓冲区资源
        buffer_matches = re.findall(r'%(\w+)\s*=.*buffer', self.content, re.IGNORECASE)
        resources['buffers'] = list(set(buffer_matches))
        
        return resources
    
    def _analyze_isa_resources(self):
        """分析ISA资源"""
        resources = {'textures': [], 'buffers': [], 'samplers': [], 'constants': []}
        
        # 查找纹理采样指令
        texture_matches = re.findall(r'image_sample.*t(\d+)', self.content)
        resources['textures'] = [f't{t}' for t in set(texture_matches)]
        
        # 查找缓冲区加载指令
        buffer_matches = re.findall(r'buffer_load.*v(\d+)', self.content)
        resources['buffers'] = [f'v{b}' for b in set(buffer_matches)]
        
        return resources
    
    def _analyze_performance(self):
        """分析性能指标"""
        self.progress_update.emit(90, "分析性能...")

        performance = {
            'estimated_cycles': 0,
            'register_pressure': 0,
            'memory_bandwidth': 0,
            'alu_utilization': 0,
            'register_analysis': self._analyze_registers(),
            'instruction_stats': self._analyze_instruction_stats(),
            'total_instruction_cycles': 0,
            'shortest_path_cycles': 0,
            'longest_path_cycles': 0,
            'bound_unit': 'Unknown',
            'functional_units': self._analyze_functional_units()
        }

        # 根据指令类型估算性能
        if self.format_type == 'ISA':
            performance.update(self._estimate_isa_performance())
        elif self.format_type == 'DXIL':
            performance.update(self._estimate_dxil_performance())
        elif self.format_type == 'HLSL_ASM':
            performance.update(self._estimate_hlsl_asm_performance())

        return performance

    def _analyze_registers(self):
        """分析寄存器使用情况"""
        register_info = {
            'work_registers': 0,
            'uniform_registers': 0,
            'stack_spilling': False,
            'register_pressure': 0
        }

        lines = self.content.splitlines()

        if self.format_type == 'ISA':
            # 分析ISA格式的寄存器使用
            v_registers = set()
            s_registers = set()

            for line in lines:
                # 查找v寄存器使用
                v_matches = re.findall(r'v(\d+)', line.lower())
                v_registers.update(v_matches)

                # 查找s寄存器使用
                s_matches = re.findall(r's(\d+)', line.lower())
                s_registers.update(s_matches)

                # 检查栈溢出
                if 'scratch' in line.lower() or 'spill' in line.lower():
                    register_info['stack_spilling'] = True

            register_info['work_registers'] = len(v_registers)
            register_info['uniform_registers'] = len(s_registers)
            register_info['register_pressure'] = min(100, len(v_registers) * 2)

        elif self.format_type == 'DXIL':
            # 分析DXIL格式的寄存器使用
            temp_registers = set()

            for line in lines:
                # 查找临时寄存器
                temp_matches = re.findall(r'%(\d+)', line)
                temp_registers.update(temp_matches)

                # 检查栈溢出
                if 'alloca' in line.lower() or 'stack' in line.lower():
                    register_info['stack_spilling'] = True

            register_info['work_registers'] = len(temp_registers)
            register_info['register_pressure'] = min(100, len(temp_registers) * 1.5)

        elif self.format_type == 'HLSL_ASM':
            # 分析HLSL汇编格式的寄存器使用
            temp_registers = set()

            for line in lines:
                # 查找临时寄存器
                temp_matches = re.findall(r'r(\d+)', line.lower())
                temp_registers.update(temp_matches)

                # 检查dcl_temps声明
                if 'dcl_temps' in line.lower():
                    temp_count_match = re.search(r'dcl_temps\s+(\d+)', line.lower())
                    if temp_count_match:
                        register_info['work_registers'] = int(temp_count_match.group(1))

            if register_info['work_registers'] == 0:
                register_info['work_registers'] = len(temp_registers)

            register_info['register_pressure'] = min(100, register_info['work_registers'] * 3)

        return register_info

    def _analyze_instruction_stats(self):
        """分析指令统计信息"""
        stats = {
            'total_instructions': 0,
            'alu_32bit': 0,
            'alu_16bit': 0,
            'complex_32bit': 0,
            'arithmetic_16bit_percent': 0
        }

        lines = self.content.splitlines()
        alu_instructions = 0
        complex_instructions = 0
        bit16_instructions = 0

        for line in lines:
            line_lower = line.strip().lower()

            if self.format_type == 'ISA':
                # ISA指令分析
                if re.match(r'^[sv]_\w+', line_lower):
                    stats['total_instructions'] += 1

                    # ALU指令
                    if any(op in line_lower for op in ['v_add', 'v_mul', 'v_mad', 'v_fma']):
                        alu_instructions += 1
                        if '_f16' in line_lower or '_h' in line_lower:
                            bit16_instructions += 1
                            stats['alu_16bit'] += 1
                        else:
                            stats['alu_32bit'] += 1

                    # 复杂指令
                    elif any(op in line_lower for op in ['v_exp', 'v_log', 'v_sin', 'v_cos', 'v_sqrt']):
                        complex_instructions += 1
                        stats['complex_32bit'] += 1

            elif self.format_type == 'DXIL':
                # DXIL指令分析
                if 'call' in line_lower and 'dx.op' in line_lower:
                    stats['total_instructions'] += 1

                    # ALU指令
                    if any(op in line_lower for op in ['fadd', 'fmul', 'fma']):
                        alu_instructions += 1
                        if 'half' in line_lower or 'i16' in line_lower:
                            bit16_instructions += 1
                            stats['alu_16bit'] += 1
                        else:
                            stats['alu_32bit'] += 1

                    # 复杂指令
                    elif any(op in line_lower for op in ['sin', 'cos', 'exp', 'log', 'sqrt']):
                        complex_instructions += 1
                        stats['complex_32bit'] += 1

            elif self.format_type == 'HLSL_ASM':
                # HLSL汇编指令分析
                if re.match(r'^(mov|add|mul|mad|dp3|dp4|tex|sample)', line_lower):
                    stats['total_instructions'] += 1

                    # ALU指令
                    if any(op in line_lower for op in ['add', 'mul', 'mad', 'dp3', 'dp4']):
                        alu_instructions += 1
                        stats['alu_32bit'] += 1  # HLSL汇编通常是32位

                    # 复杂指令
                    elif any(op in line_lower for op in ['sin', 'cos', 'exp', 'log', 'sqrt']):
                        complex_instructions += 1
                        stats['complex_32bit'] += 1

        # 计算16位算术运算比例
        if alu_instructions > 0:
            stats['arithmetic_16bit_percent'] = (bit16_instructions / alu_instructions) * 100

        return stats

    def _analyze_functional_units(self):
        """分析功能单元使用情况"""
        units = {
            'fma_cycles': 0,
            'cvt_cycles': 0,
            'sfu_cycles': 0,
            'ls_cycles': 0,
            'texture_cycles': 0,
            'varying_cycles': 0
        }

        lines = self.content.splitlines()

        for line in lines:
            line_lower = line.strip().lower()

            if self.format_type == 'ISA':
                # FMA单元 (算术运算)
                if any(op in line_lower for op in ['v_add', 'v_mul', 'v_mad', 'v_fma']):
                    units['fma_cycles'] += 1

                # CVT单元 (转换和分支)
                elif any(op in line_lower for op in ['v_cvt', 'v_cmp', 's_branch', 's_cbranch']):
                    units['cvt_cycles'] += 1

                # SFU单元 (特殊函数)
                elif any(op in line_lower for op in ['v_exp', 'v_log', 'v_sin', 'v_cos', 'v_sqrt', 'v_rcp', 'v_rsq']):
                    units['sfu_cycles'] += 4  # SFU指令通常需要更多周期

                # LS单元 (加载/存储)
                elif any(op in line_lower for op in ['buffer_load', 'buffer_store', 's_load', 's_store']):
                    units['ls_cycles'] += 2

                # 纹理单元
                elif any(op in line_lower for op in ['image_sample', 'image_load']):
                    units['texture_cycles'] += 4

                # 变量插值
                elif any(op in line_lower for op in ['v_interp']):
                    units['varying_cycles'] += 1

            elif self.format_type == 'DXIL':
                # 根据DXIL操作分类
                if 'dx.op' in line_lower:
                    if any(op in line_lower for op in ['fadd', 'fmul', 'fma']):
                        units['fma_cycles'] += 1
                    elif any(op in line_lower for op in ['uitofp', 'fptosi', 'fptoui']):
                        units['cvt_cycles'] += 1
                    elif any(op in line_lower for op in ['sin', 'cos', 'exp', 'log', 'sqrt']):
                        units['sfu_cycles'] += 4
                    elif any(op in line_lower for op in ['load', 'store']):
                        units['ls_cycles'] += 2
                    elif any(op in line_lower for op in ['sample', 'texture']):
                        units['texture_cycles'] += 4

        return units

    def _estimate_hlsl_asm_performance(self):
        """估算HLSL汇编性能"""
        lines = self.content.splitlines()
        instruction_count = 0
        alu_ops = 0
        memory_ops = 0

        for line in lines:
            line_lower = line.strip().lower()
            if re.match(r'^(mov|add|mul|mad|dp3|dp4|tex|sample)', line_lower):
                instruction_count += 1
                if any(op in line_lower for op in ['add', 'mul', 'mad', 'dp3', 'dp4']):
                    alu_ops += 1
                elif any(op in line_lower for op in ['tex', 'sample']):
                    memory_ops += 1

        return {
            'total_instruction_cycles': instruction_count * 1.5,
            'shortest_path_cycles': instruction_count,
            'longest_path_cycles': instruction_count * 2,
            'bound_unit': 'A (Arithmetic)' if alu_ops > memory_ops else 'T (Texture)',
            'alu_utilization': min(100, alu_ops * 2),
            'memory_bandwidth': memory_ops * 16
        }

    def _estimate_isa_performance(self):
        """估算ISA性能"""
        lines = self.content.splitlines()
        alu_ops = 0
        memory_ops = 0
        sfu_ops = 0
        texture_ops = 0

        for line in lines:
            line_lower = line.lower()
            if re.search(r'v_(add|mul|mad|fma)', line_lower):
                alu_ops += 1
            elif re.search(r'(buffer_load|buffer_store|s_load)', line_lower):
                memory_ops += 1
            elif re.search(r'v_(exp|log|sin|cos|sqrt|rcp|rsq)', line_lower):
                sfu_ops += 1
            elif re.search(r'image_sample', line_lower):
                texture_ops += 1

        # 计算各种周期
        total_cycles = alu_ops + memory_ops * 4 + sfu_ops * 4 + texture_ops * 8

        # 确定瓶颈单元
        unit_cycles = {
            'A (Arithmetic)': alu_ops,
            'LS (Load/Store)': memory_ops * 4,
            'SFU (Special Function)': sfu_ops * 4,
            'T (Texture)': texture_ops * 8
        }
        bound_unit = max(unit_cycles.items(), key=lambda x: x[1])[0] if unit_cycles else 'Unknown'

        return {
            'total_instruction_cycles': total_cycles,
            'shortest_path_cycles': total_cycles // 2,  # 假设最短路径是一半
            'longest_path_cycles': total_cycles * 2,    # 假设最长路径是两倍
            'bound_unit': bound_unit,
            'estimated_cycles': total_cycles,
            'alu_utilization': min(100, alu_ops * 2),
            'memory_bandwidth': memory_ops * 16
        }
    
    def _estimate_dxil_performance(self):
        """估算DXIL性能"""
        lines = self.content.splitlines()
        alu_ops = 0
        memory_ops = 0
        sfu_ops = 0
        texture_ops = 0

        for line in lines:
            line_lower = line.lower()
            if 'call' in line_lower and 'dx.op' in line_lower:
                if any(op in line_lower for op in ['fadd', 'fmul', 'fma']):
                    alu_ops += 1
                elif any(op in line_lower for op in ['load', 'store']):
                    memory_ops += 1
                elif any(op in line_lower for op in ['sin', 'cos', 'exp', 'log', 'sqrt']):
                    sfu_ops += 1
                elif any(op in line_lower for op in ['sample', 'texture']):
                    texture_ops += 1

        # 计算各种周期
        total_cycles = alu_ops + memory_ops * 3 + sfu_ops * 4 + texture_ops * 6

        # 确定瓶颈单元
        unit_cycles = {
            'A (Arithmetic)': alu_ops,
            'LS (Load/Store)': memory_ops * 3,
            'SFU (Special Function)': sfu_ops * 4,
            'T (Texture)': texture_ops * 6
        }
        bound_unit = max(unit_cycles.items(), key=lambda x: x[1])[0] if unit_cycles else 'Unknown'

        return {
            'total_instruction_cycles': total_cycles,
            'shortest_path_cycles': total_cycles // 2,
            'longest_path_cycles': total_cycles * 2,
            'bound_unit': bound_unit,
            'estimated_cycles': total_cycles,
            'alu_utilization': min(100, alu_ops),
            'memory_bandwidth': memory_ops * 8
        }


class ShaderAnalysisDialog(QDialog):
    """Shader编译结果数据分析弹窗"""

    def __init__(self, content, parent=None):
        super().__init__(parent)
        self.content = content
        self.analysis_result = None

        self.setWindowTitle('Shader编译结果数据分析')
        self.setModal(True)
        self.resize(1000, 700)

        # 检测格式
        self.format_type = ShaderFormatDetector.detect_format(content)

        self.setup_ui()
        self.start_analysis()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题和格式信息
        title_layout = QHBoxLayout()
        title_label = QLabel(f'📊 Shader数据分析 - 检测格式: {self.format_type}')
        title_label.setFont(QFont('Arial', 12, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel('正在分析...')
        layout.addWidget(self.progress_label)
        layout.addWidget(self.progress_bar)

        # 主要内容区域 - 使用Tab页面
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 创建各个Tab页面
        self.create_overview_performance_tab()
        self.create_instructions_tab()
        self.create_resources_tab()
        self.create_visualization_tab()

        # 按钮
        button_layout = QHBoxLayout()
        self.btn_export = QPushButton('导出分析报告')
        self.btn_export.clicked.connect(self.export_analysis)
        self.btn_export.setEnabled(False)

        self.btn_close = QPushButton('关闭')
        self.btn_close.clicked.connect(self.accept)

        button_layout.addWidget(self.btn_export)
        button_layout.addStretch()
        button_layout.addWidget(self.btn_close)
        layout.addLayout(button_layout)

    def create_overview_performance_tab(self):
        """创建概览和性能合并Tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 基本信息组
        basic_group = QGroupBox('基本信息')
        basic_layout = QGridLayout(basic_group)

        self.format_label = QLabel('格式: 检测中...')
        self.size_label = QLabel('大小: 检测中...')
        self.lines_label = QLabel('行数: 检测中...')
        self.encoding_label = QLabel('编码: 检测中...')

        basic_layout.addWidget(QLabel('检测格式:'), 0, 0)
        basic_layout.addWidget(self.format_label, 0, 1)
        basic_layout.addWidget(QLabel('文件大小:'), 1, 0)
        basic_layout.addWidget(self.size_label, 1, 1)
        basic_layout.addWidget(QLabel('总行数:'), 2, 0)
        basic_layout.addWidget(self.lines_label, 2, 1)
        basic_layout.addWidget(QLabel('字符编码:'), 3, 0)
        basic_layout.addWidget(self.encoding_label, 3, 1)

        layout.addWidget(basic_group)

        # 寄存器信息组
        register_group = QGroupBox('寄存器分析')
        register_layout = QGridLayout(register_group)

        self.work_registers_label = QLabel('Work Registers: 计算中...')
        self.uniform_registers_label = QLabel('Uniform Registers: 计算中...')
        self.stack_spilling_label = QLabel('Stack Spilling: 计算中...')
        self.register_pressure_label = QLabel('Register Pressure: 计算中...')

        register_layout.addWidget(QLabel('工作寄存器:'), 0, 0)
        register_layout.addWidget(self.work_registers_label, 0, 1)
        register_layout.addWidget(QLabel('统一寄存器:'), 1, 0)
        register_layout.addWidget(self.uniform_registers_label, 1, 1)
        register_layout.addWidget(QLabel('栈溢出:'), 2, 0)
        register_layout.addWidget(self.stack_spilling_label, 2, 1)
        register_layout.addWidget(QLabel('寄存器压力:'), 3, 0)
        register_layout.addWidget(self.register_pressure_label, 3, 1)

        layout.addWidget(register_group)

        # 指令统计组
        instruction_stats_group = QGroupBox('指令统计')
        instruction_stats_layout = QGridLayout(instruction_stats_group)

        self.total_instructions_label = QLabel('总指令数: 计算中...')
        self.alu_32bit_label = QLabel('ALU指令数(32位): 计算中...')
        self.alu_16bit_label = QLabel('ALU指令数(16位): 计算中...')
        self.complex_32bit_label = QLabel('复杂指令数(32位): 计算中...')
        self.arithmetic_16bit_percent_label = QLabel('16位算术运算比例: 计算中...')

        instruction_stats_layout.addWidget(QLabel('总指令数:'), 0, 0)
        instruction_stats_layout.addWidget(self.total_instructions_label, 0, 1)
        instruction_stats_layout.addWidget(QLabel('ALU指令(32位):'), 1, 0)
        instruction_stats_layout.addWidget(self.alu_32bit_label, 1, 1)
        instruction_stats_layout.addWidget(QLabel('ALU指令(16位):'), 2, 0)
        instruction_stats_layout.addWidget(self.alu_16bit_label, 2, 1)
        instruction_stats_layout.addWidget(QLabel('复杂指令(32位):'), 3, 0)
        instruction_stats_layout.addWidget(self.complex_32bit_label, 3, 1)
        instruction_stats_layout.addWidget(QLabel('16位算术比例:'), 4, 0)
        instruction_stats_layout.addWidget(self.arithmetic_16bit_percent_label, 4, 1)

        layout.addWidget(instruction_stats_group)

        # 性能指标组
        perf_group = QGroupBox('性能指标')
        perf_layout = QGridLayout(perf_group)

        self.total_cycles_label = QLabel('总指令周期: 计算中...')
        self.shortest_path_label = QLabel('最短路径周期: 计算中...')
        self.longest_path_label = QLabel('最长路径周期: 计算中...')
        self.bound_unit_label = QLabel('瓶颈单元: 计算中...')

        perf_layout.addWidget(QLabel('总指令周期:'), 0, 0)
        perf_layout.addWidget(self.total_cycles_label, 0, 1)
        perf_layout.addWidget(QLabel('最短路径周期:'), 1, 0)
        perf_layout.addWidget(self.shortest_path_label, 1, 1)
        perf_layout.addWidget(QLabel('最长路径周期:'), 2, 0)
        perf_layout.addWidget(self.longest_path_label, 2, 1)
        perf_layout.addWidget(QLabel('瓶颈单元:'), 3, 0)
        perf_layout.addWidget(self.bound_unit_label, 3, 1)

        layout.addWidget(perf_group)

        # 功能单元分析组
        functional_units_group = QGroupBox('功能单元分析 (Mali架构)')
        functional_units_layout = QGridLayout(functional_units_group)

        self.fma_cycles_label = QLabel('FMA (算术): 计算中...')
        self.cvt_cycles_label = QLabel('CVT (转换): 计算中...')
        self.sfu_cycles_label = QLabel('SFU (特殊函数): 计算中...')
        self.ls_cycles_label = QLabel('LS (加载/存储): 计算中...')
        self.texture_cycles_label = QLabel('T (纹理操作): 计算中...')
        self.varying_cycles_label = QLabel('V (变量插值): 计算中...')

        functional_units_layout.addWidget(QLabel('FMA单元:'), 0, 0)
        functional_units_layout.addWidget(self.fma_cycles_label, 0, 1)
        functional_units_layout.addWidget(QLabel('CVT单元:'), 1, 0)
        functional_units_layout.addWidget(self.cvt_cycles_label, 1, 1)
        functional_units_layout.addWidget(QLabel('SFU单元:'), 2, 0)
        functional_units_layout.addWidget(self.sfu_cycles_label, 2, 1)
        functional_units_layout.addWidget(QLabel('LS单元:'), 3, 0)
        functional_units_layout.addWidget(self.ls_cycles_label, 3, 1)
        functional_units_layout.addWidget(QLabel('纹理单元:'), 4, 0)
        functional_units_layout.addWidget(self.texture_cycles_label, 4, 1)
        functional_units_layout.addWidget(QLabel('变量单元:'), 5, 0)
        functional_units_layout.addWidget(self.varying_cycles_label, 5, 1)

        layout.addWidget(functional_units_group)

        # 优化建议
        advice_group = QGroupBox('优化建议')
        advice_layout = QVBoxLayout(advice_group)
        self.advice_text = QTextEdit()
        self.advice_text.setReadOnly(True)
        self.advice_text.setMaximumHeight(150)
        advice_layout.addWidget(self.advice_text)

        layout.addWidget(advice_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, '� 概览与性能')



    def create_instructions_tab(self):
        """创建指令Tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 指令列表
        self.instructions_table = QTableWidget()
        self.instructions_table.setColumnCount(3)
        self.instructions_table.setHorizontalHeaderLabels(['类型', '指令', '完整行'])
        self.instructions_table.horizontalHeader().setStretchLastSection(True)

        layout.addWidget(QLabel('⚙️ 指令分析'))
        layout.addWidget(self.instructions_table)

        self.tab_widget.addTab(tab, '⚙️ 指令')

    def create_resources_tab(self):
        """创建资源Tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 资源使用情况
        resources_layout = QGridLayout()

        # 纹理资源
        texture_group = QGroupBox('纹理资源')
        texture_layout = QVBoxLayout(texture_group)
        self.texture_list = QTextEdit()
        self.texture_list.setMaximumHeight(100)
        self.texture_list.setReadOnly(True)
        texture_layout.addWidget(self.texture_list)

        # 缓冲区资源
        buffer_group = QGroupBox('缓冲区资源')
        buffer_layout = QVBoxLayout(buffer_group)
        self.buffer_list = QTextEdit()
        self.buffer_list.setMaximumHeight(100)
        self.buffer_list.setReadOnly(True)
        buffer_layout.addWidget(self.buffer_list)

        # 采样器资源
        sampler_group = QGroupBox('采样器资源')
        sampler_layout = QVBoxLayout(sampler_group)
        self.sampler_list = QTextEdit()
        self.sampler_list.setMaximumHeight(100)
        self.sampler_list.setReadOnly(True)
        sampler_layout.addWidget(self.sampler_list)

        # 常量资源
        constant_group = QGroupBox('常量资源')
        constant_layout = QVBoxLayout(constant_group)
        self.constant_list = QTextEdit()
        self.constant_list.setMaximumHeight(100)
        self.constant_list.setReadOnly(True)
        constant_layout.addWidget(self.constant_list)

        resources_layout.addWidget(texture_group, 0, 0)
        resources_layout.addWidget(buffer_group, 0, 1)
        resources_layout.addWidget(sampler_group, 1, 0)
        resources_layout.addWidget(constant_group, 1, 1)

        layout.addWidget(QLabel('🎯 资源使用分析'))
        layout.addLayout(resources_layout)

        self.tab_widget.addTab(tab, '🎯 资源')



    def create_visualization_tab(self):
        """创建可视化Tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建matplotlib图表
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)

        layout.addWidget(QLabel('📈 数据可视化'))
        layout.addWidget(self.canvas)

        self.tab_widget.addTab(tab, '📈 可视化')

    def start_analysis(self):
        """开始分析"""
        self.worker = ShaderAnalysisWorker(self.content, self.format_type)
        self.worker.progress_update.connect(self.update_progress)
        self.worker.analysis_complete.connect(self.on_analysis_complete)
        self.worker.start()

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)

    def on_analysis_complete(self, result):
        """分析完成"""
        if 'error' in result:
            self.progress_label.setText(f'分析失败: {result["error"]}')
            return

        self.analysis_result = result
        self.progress_bar.hide()
        self.progress_label.hide()
        self.btn_export.setEnabled(True)

        # 更新各个Tab的内容
        self.update_overview_performance_tab(result)
        self.update_instructions_tab(result)
        self.update_resources_tab(result)
        self.update_visualization_tab(result)

    def update_overview_performance_tab(self, result):
        """更新概览和性能Tab"""
        basic_info = result.get('basic_info', {})
        performance = result.get('performance', {})

        # 更新基本信息
        self.format_label.setText(f"{basic_info.get('format', 'Unknown')}")
        self.size_label.setText(f"{basic_info.get('size', 0):,} 字节")
        self.lines_label.setText(f"{basic_info.get('lines', 0):,} 行")
        self.encoding_label.setText(f"{basic_info.get('encoding', 'Unknown')}")

        # 更新寄存器信息
        register_info = performance.get('register_analysis', {})
        self.work_registers_label.setText(f"{register_info.get('work_registers', 0)} 个")
        self.uniform_registers_label.setText(f"{register_info.get('uniform_registers', 0)} 个")
        self.stack_spilling_label.setText("是" if register_info.get('stack_spilling', False) else "否")
        self.register_pressure_label.setText(f"{register_info.get('register_pressure', 0)}%")

        # 更新指令统计
        instruction_stats = performance.get('instruction_stats', {})
        self.total_instructions_label.setText(f"{instruction_stats.get('total_instructions', 0)} 条")
        self.alu_32bit_label.setText(f"{instruction_stats.get('alu_32bit', 0)} 条")
        self.alu_16bit_label.setText(f"{instruction_stats.get('alu_16bit', 0)} 条")
        self.complex_32bit_label.setText(f"{instruction_stats.get('complex_32bit', 0)} 条")
        self.arithmetic_16bit_percent_label.setText(f"{instruction_stats.get('arithmetic_16bit_percent', 0):.1f}%")

        # 更新性能指标
        self.total_cycles_label.setText(f"{performance.get('total_instruction_cycles', 0):,} 周期")
        self.shortest_path_label.setText(f"{performance.get('shortest_path_cycles', 0):,} 周期")
        self.longest_path_label.setText(f"{performance.get('longest_path_cycles', 0):,} 周期")
        self.bound_unit_label.setText(f"{performance.get('bound_unit', 'Unknown')}")

        # 更新功能单元分析
        functional_units = performance.get('functional_units', {})
        self.fma_cycles_label.setText(f"{functional_units.get('fma_cycles', 0):,} 周期")
        self.cvt_cycles_label.setText(f"{functional_units.get('cvt_cycles', 0):,} 周期")
        self.sfu_cycles_label.setText(f"{functional_units.get('sfu_cycles', 0):,} 周期")
        self.ls_cycles_label.setText(f"{functional_units.get('ls_cycles', 0):,} 周期")
        self.texture_cycles_label.setText(f"{functional_units.get('texture_cycles', 0):,} 周期")
        self.varying_cycles_label.setText(f"{functional_units.get('varying_cycles', 0):,} 周期")

        # 生成优化建议
        advice = self.generate_optimization_advice(performance, result)
        self.advice_text.setPlainText(advice)

    def update_instructions_tab(self, result):
        """更新指令Tab"""
        instructions = result.get('instructions', [])

        self.instructions_table.setRowCount(len(instructions))

        for row, instruction in enumerate(instructions):
            type_item = QTableWidgetItem(instruction.get('type', ''))
            name_item = QTableWidgetItem(instruction.get('name', ''))
            line_item = QTableWidgetItem(instruction.get('line', ''))

            self.instructions_table.setItem(row, 0, type_item)
            self.instructions_table.setItem(row, 1, name_item)
            self.instructions_table.setItem(row, 2, line_item)

        self.instructions_table.resizeColumnsToContents()

    def update_resources_tab(self, result):
        """更新资源Tab"""
        resources = result.get('resources', {})

        # 更新纹理列表
        textures = resources.get('textures', [])
        self.texture_list.setPlainText('\n'.join(textures) if textures else '未检测到纹理资源')

        # 更新缓冲区列表
        buffers = resources.get('buffers', [])
        self.buffer_list.setPlainText('\n'.join(buffers) if buffers else '未检测到缓冲区资源')

        # 更新采样器列表
        samplers = resources.get('samplers', [])
        self.sampler_list.setPlainText('\n'.join(samplers) if samplers else '未检测到采样器资源')

        # 更新常量列表
        constants = resources.get('constants', [])
        self.constant_list.setPlainText('\n'.join(constants) if constants else '未检测到常量资源')



    def generate_optimization_advice(self, performance, result):
        """生成优化建议"""
        advice = []

        cycles = performance.get('estimated_cycles', 0)
        alu_util = performance.get('alu_utilization', 0)
        bandwidth = performance.get('memory_bandwidth', 0)

        if cycles > 1000:
            advice.append("⚠️ 指令数量较多，考虑简化算法或使用更高效的指令")

        if alu_util > 80:
            advice.append("⚠️ ALU利用率较高，可能存在计算瓶颈")

        if bandwidth > 1024:
            advice.append("⚠️ 内存带宽需求较高，考虑减少纹理采样或优化数据访问模式")

        # 根据格式给出特定建议
        format_type = result.get('format', '')
        if format_type == 'DXIL':
            advice.append("💡 DXIL格式：考虑使用更高的优化级别(-O2, -O3)")
        elif format_type == 'ISA':
            advice.append("💡 ISA格式：检查是否有冗余的寄存器移动指令")

        if not advice:
            advice.append("✅ 当前shader性能表现良好，无明显优化建议")

        return '\n\n'.join(advice)

    def update_visualization_tab(self, result):
        """更新可视化Tab"""
        self.figure.clear()

        # 创建子图
        ax1 = self.figure.add_subplot(2, 2, 1)
        ax2 = self.figure.add_subplot(2, 2, 2)
        ax3 = self.figure.add_subplot(2, 2, 3)
        ax4 = self.figure.add_subplot(2, 2, 4)

        # 统计信息饼图
        stats = result.get('statistics', {})
        if stats:
            labels = []
            sizes = []

            total_lines = stats.get('total_lines', 1)
            non_empty = stats.get('non_empty_lines', 0)
            comments = stats.get('comment_lines', 0)
            instructions = stats.get('instruction_lines', 0)
            empty = total_lines - non_empty

            if empty > 0:
                labels.append('空行')
                sizes.append(empty)
            if comments > 0:
                labels.append('注释行')
                sizes.append(comments)
            if instructions > 0:
                labels.append('指令行')
                sizes.append(instructions)
            if non_empty - comments - instructions > 0:
                labels.append('其他行')
                sizes.append(non_empty - comments - instructions)

            if sizes:
                ax1.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
                ax1.set_title('代码行分布', fontsize=12)

        # 指令类型分布
        instructions = result.get('instructions', [])
        if instructions:
            instruction_types = {}
            for inst in instructions:
                inst_type = inst.get('type', 'Unknown')
                instruction_types[inst_type] = instruction_types.get(inst_type, 0) + 1

            if instruction_types:
                types = list(instruction_types.keys())
                counts = list(instruction_types.values())

                ax2.bar(types, counts)
                ax2.set_title('指令类型分布', fontsize=12)
                ax2.set_xlabel('指令类型', fontsize=10)
                ax2.set_ylabel('数量', fontsize=10)
                plt.setp(ax2.get_xticklabels(), rotation=45, ha='right', fontsize=9)

        # 性能指标雷达图
        performance = result.get('performance', {})
        if performance:
            categories = ['执行周期', 'ALU利用率', '内存带宽', '寄存器压力']
            register_analysis = performance.get('register_analysis', {})
            values = [
                min(100, performance.get('estimated_cycles', 0) / 10),  # 归一化到0-100
                performance.get('alu_utilization', 0),
                min(100, performance.get('memory_bandwidth', 0) / 50),  # 归一化到0-100
                register_analysis.get('register_pressure', 0)
            ]

            # 创建雷达图
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]

            ax3.plot(angles, values, 'o-', linewidth=2)
            ax3.fill(angles, values, alpha=0.25)
            ax3.set_xticks(angles[:-1])
            ax3.set_xticklabels(categories, fontsize=10)
            ax3.set_ylim(0, 100)
            ax3.set_title('性能指标雷达图', fontsize=12)
            ax3.grid(True)

        # 资源使用情况
        resources = result.get('resources', {})
        if resources:
            resource_types = ['纹理', '缓冲区', '采样器', '常量']
            resource_counts = [
                len(resources.get('textures', [])),
                len(resources.get('buffers', [])),
                len(resources.get('samplers', [])),
                len(resources.get('constants', []))
            ]

            ax4.bar(resource_types, resource_counts, color=['red', 'green', 'blue', 'orange'])
            ax4.set_title('资源使用统计', fontsize=12)
            ax4.set_xlabel('资源类型', fontsize=10)
            ax4.set_ylabel('数量', fontsize=10)
            ax4.tick_params(axis='x', labelsize=9)
            ax4.tick_params(axis='y', labelsize=9)

        self.figure.tight_layout()
        self.canvas.draw()

    def export_analysis(self):
        """导出分析报告"""
        if not self.analysis_result:
            return

        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            '导出分析报告',
            f'shader_analysis_{self.format_type.lower()}.txt',
            'Text Files (*.txt);;All Files (*)'
        )

        if file_path:
            try:
                self.save_analysis_report(file_path)
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, '导出成功', f'分析报告已保存到:\n{file_path}')
            except Exception as e:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, '导出失败', f'保存报告时发生错误:\n{str(e)}')

    def save_analysis_report(self, file_path):
        """保存分析报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("Shader编译结果数据分析报告\n")
            f.write("=" * 50 + "\n\n")

            # 基本信息
            basic_info = self.analysis_result.get('basic_info', {})
            f.write("基本信息:\n")
            f.write("-" * 20 + "\n")
            for key, value in basic_info.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")

            # 统计信息
            stats = self.analysis_result.get('statistics', {})
            f.write("统计信息:\n")
            f.write("-" * 20 + "\n")
            for key, value in stats.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")

            # 指令分析
            instructions = self.analysis_result.get('instructions', [])
            f.write(f"指令分析 (共{len(instructions)}条):\n")
            f.write("-" * 20 + "\n")
            for inst in instructions[:20]:  # 只显示前20条
                f.write(f"{inst.get('type', '')}: {inst.get('name', '')} - {inst.get('line', '')}\n")
            if len(instructions) > 20:
                f.write(f"... 还有{len(instructions) - 20}条指令\n")
            f.write("\n")

            # 资源分析
            resources = self.analysis_result.get('resources', {})
            f.write("资源分析:\n")
            f.write("-" * 20 + "\n")
            for res_type, res_list in resources.items():
                f.write(f"{res_type}: {', '.join(res_list) if res_list else '无'}\n")
            f.write("\n")

            # 性能分析
            performance = self.analysis_result.get('performance', {})
            f.write("性能分析:\n")
            f.write("-" * 20 + "\n")
            for key, value in performance.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")

            # 优化建议
            advice = self.generate_optimization_advice(performance, self.analysis_result)
            f.write("优化建议:\n")
            f.write("-" * 20 + "\n")
            f.write(advice)
