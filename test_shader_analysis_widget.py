#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试shader_analysis_widget的布局问题修复
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from shader_analysis_widget import ShaderAnalysisWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Shader Analysis Widget 布局测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加shader analysis widget
        self.shader_widget = ShaderAnalysisWidget()
        layout.addWidget(self.shader_widget)
        
        # 模拟一些测试数据
        self.simulate_analysis_data()
    
    def simulate_analysis_data(self):
        """模拟分析数据以测试布局"""
        # 模拟分析结果数据
        test_result = {
            'analysis': {
                'overall_statistics': {
                    'total_nodes': 1250,
                    'total_variables': 45,
                    'total_intermediate_results': 320,
                    'total_type_conversions': 15,
                    'total_precision_issues': 3,
                    'total_lines': 85,
                    'precision_accuracy_score': 87.5,
                    'type_distribution': {
                        'float': 450,
                        'vec3': 280,
                        'vec4': 220,
                        'mat4': 150,
                        'int': 100,
                        'bool': 50
                    }
                },
                'comparison': {
                    'operation_count_improvement': {
                        'improvement': 125,
                        'improvement_percentage': 25.5
                    }
                }
            },
            'files': {
                'html': 'test_report.html',
                'json': 'test_data.json'
            }
        }
        
        # 延迟更新数据以模拟真实场景
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(2000, lambda: self.shader_widget.update_display(test_result))

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
