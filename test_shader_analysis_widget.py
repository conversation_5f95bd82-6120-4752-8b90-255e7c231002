#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试shader_analysis_widget的布局问题修复
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from shader_analysis_widget import ShaderAnalysisWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Shader Analysis Widget 布局测试 - 修复版本")
        self.setGeometry(100, 100, 900, 700)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # 添加说明标签
        info_label = QWidget()
        info_layout = QVBoxLayout(info_label)
        title = QWidget()
        from PyQt5.QtWidgets import QLabel
        title_label = QLabel("🔧 布局修复测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333; margin-bottom: 5px;")
        desc_label = QLabel("此版本修复了初始化时指标卡片区域过扁的问题。现在应该在初始化时就有正确的高度。")
        desc_label.setStyleSheet("font-size: 12px; color: #666; margin-bottom: 10px;")
        desc_label.setWordWrap(True)
        info_layout.addWidget(title_label)
        info_layout.addWidget(desc_label)
        layout.addWidget(info_label)

        # 添加shader analysis widget
        self.shader_widget = ShaderAnalysisWidget()
        layout.addWidget(self.shader_widget)

        # 模拟一些测试数据
        self.simulate_analysis_data()

        print("✅ 测试窗口初始化完成，请观察指标卡片区域是否有正确的高度")
    
    def simulate_analysis_data(self):
        """模拟分析数据以测试布局"""
        # 模拟分析结果数据
        test_result = {
            'analysis': {
                'overall_statistics': {
                    'total_nodes': 1250,
                    'total_variables': 45,
                    'total_intermediate_results': 320,
                    'total_type_conversions': 15,
                    'total_precision_issues': 3,
                    'total_lines': 85,
                    'precision_accuracy_score': 87.5,
                    'type_distribution': {
                        'float': 450,
                        'vec3': 280,
                        'vec4': 220,
                        'mat4': 150,
                        'int': 100,
                        'bool': 50
                    }
                },
                'comparison': {
                    'operation_count_improvement': {
                        'improvement': 125,
                        'improvement_percentage': 25.5
                    }
                }
            },
            'files': {
                'html': 'test_report.html',
                'json': 'test_data.json'
            }
        }
        
        # 延迟更新数据以模拟真实场景
        from PyQt5.QtCore import QTimer
        print("⏰ 2秒后将加载测试数据...")
        QTimer.singleShot(2000, lambda: self.load_test_data(test_result))

    def load_test_data(self, test_result):
        """加载测试数据"""
        print("📊 正在加载测试数据...")
        self.shader_widget.update_display(test_result)
        print("✅ 测试数据加载完成，请观察布局是否正确更新")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
