from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QComboBox, QLineEdit, QPushButton, qApp, QHBoxLayout, QFileDialog, QMessageBox
from PyQt5.QtCore import pyqtSignal, Qt, QThread, QObject

# 导入编译器API静态类
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Process', 'compiler_explorer'))
from compiler_api import CompilerExplorerAPI

# 导入分析弹窗
from .shader_compile_dialog import CompilerHelpDialog, ShaderAnalysisDialog


class CompileWorker(QObject):
    """异步编译工作线程"""
    # 定义信号
    compile_finished = pyqtSignal(str)  # 编译完成信号，传递结果
    compile_error = pyqtSignal(str)     # 编译错误信号，传递错误信息

    def __init__(self):
        super().__init__()
        self.source_code = ""
        self.compiler_id = ""
        self.options = {}

    def set_compile_params(self, source_code, compiler_id, options):
        """设置编译参数"""
        self.source_code = source_code
        self.compiler_id = compiler_id
        self.options = options

    def compile(self):
        """执行编译"""
        try:
            # 调用API进行编译
            result = CompilerExplorerAPI.compile_hlsl_source_with_compiler(
                self.source_code, self.compiler_id, self.options
            )
            self.compile_finished.emit(result)
        except Exception as e:
            self.compile_error.emit(f"编译过程中发生错误：{str(e)}")


class CompilerListWorker(QObject):
    """异步获取编译器列表的工作线程"""
    # 定义信号
    list_finished = pyqtSignal(list)    # 获取完成信号，传递编译器列表
    list_error = pyqtSignal(str)        # 获取错误信号，传递错误信息

    def fetch_compilers(self):
        """获取编译器列表"""
        try:
            compiler_list = CompilerExplorerAPI.fetch_compiler_list()
            self.list_finished.emit(compiler_list)
        except Exception as e:
            self.list_error.emit(f"获取编译器列表失败：{str(e)}")



class CompilerExplorerPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.compiler_name_id_map = {}  # name->id

        # 初始化异步工作线程
        self.compile_thread = None
        self.compile_worker = None
        self.compiler_list_thread = None
        self.compiler_list_worker = None

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)

        # 设置面板的最小宽度，防止UI元素被过度挤压
        self.setMinimumWidth(450)  # 确保有足够空间显示所有控件
        # 第一行：编译器选择、编译、测试按钮
        row1 = QHBoxLayout()
        row1.setSpacing(6)  # 减少间距避免挤压
        self.label = QLabel('编译器:')  # 缩短标签文字
        self.label.setStyleSheet('padding-left: 8px;')
        self.label.setMinimumWidth(50)  # 减少标签宽度
        self.label.setMaximumWidth(60)  # 固定标签宽度

        self.combo = QComboBox()
        self.combo.setMinimumWidth(120)  # 减少最小宽度，但保持可用性
        # 设置ComboBox的尺寸策略，允许收缩
        from PyQt5.QtWidgets import QSizePolicy
        self.combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 创建按钮并设置更紧凑的尺寸
        self.btn_compile = QPushButton('编译')
        self.btn_import = QPushButton('导入')
        self.btn_export = QPushButton('导出')
        self.btn_analyze = QPushButton('分析')  # 缩短按钮文字
        self.btn_analyze.setToolTip('分析当前编译结果的数据结构和性能')

        # 设置按钮的紧凑样式
        button_min_width = 45  # 减少按钮最小宽度
        button_max_width = 60  # 设置最大宽度防止过宽
        for btn in [self.btn_compile, self.btn_import, self.btn_export, self.btn_analyze]:
            btn.setMinimumWidth(button_min_width)
            btn.setMaximumWidth(button_max_width)
            btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # 添加控件到布局
        row1.addWidget(self.label)
        row1.addWidget(self.combo, 1)  # 拉伸因子为1，但会受到最小宽度限制
        row1.addWidget(self.btn_compile)
        row1.addWidget(self.btn_import)
        row1.addWidget(self.btn_export)
        row1.addWidget(self.btn_analyze)
        # 第二行：参数label和输入框
        row2 = QHBoxLayout()
        row2.setSpacing(8)
        self.options_label = QLabel('编译参数:')
        self.options_label.setStyleSheet('padding-left: 12px;')
        self.options_edit = QLineEdit('-T ps_6_0 -E main')
        self.options_edit.setPlaceholderText('例如: -T ps_6_0 -E main -O2')

        # 帮助按钮
        self.btn_help = QPushButton('?')
        self.btn_help.setMaximumWidth(25)
        self.btn_help.setToolTip('点击查看编译参数说明')
        self.btn_help.clicked.connect(self.show_help)

        row2.addWidget(self.options_label)
        row2.addWidget(self.options_edit, 1)
        row2.addWidget(self.btn_help)
        row2.addStretch(0)

        layout.addLayout(row1)
        layout.addLayout(row2)
        self.setLayout(layout)
        self.btn_compile.clicked.connect(self.compile_and_emit)
        self.btn_import.clicked.connect(self.import_file)
        self.btn_export.clicked.connect(self.export_result)
        self.btn_analyze.clicked.connect(self.analyze_current_data)
        self.combo.currentTextChanged.connect(self.on_compiler_changed)
        self.fetch_compiler_list()

        # 存储最后的编译结果
        self.current_text = ""

    def show_help(self):
        """显示帮助弹窗（非模态）"""
        dialog = CompilerHelpDialog(self)
        dialog.setModal(False)  # 设置为非模态
        dialog.show()  # 使用show()而不是exec_()

    def analyze_current_data(self):
        """分析当前文档内容的数据"""
        if not self.current_text:
            self._show_non_blocking_message("分析失败", "没有可分析的数据！\n请先进行编译或导入文件。", "warning")
            return

        # 创建并显示分析弹窗（非模态）
        try:
            dialog = ShaderAnalysisDialog(self.current_text, self)
            dialog.setModal(False)  # 设置为非模态
            dialog.show()  # 使用show()而不是exec_()
        except Exception as e:
            self._show_non_blocking_message("分析失败", f"启动数据分析时发生错误：\n{str(e)}", "error")

    def is_rga_290(self, compiler_name):
        """检测是否为RGA 2.9.0版本"""
        return 'rga' in compiler_name.lower() and '2.9.0' in compiler_name.lower()

    def on_compiler_changed(self):
        """当编译器选择改变时，建议合适的参数"""
        compiler_name = self.combo.currentText().lower()
        current_params = self.options_edit.text().strip()

        # 检查当前参数是否与新编译器兼容
        is_rga_290 = self.is_rga_290(compiler_name)
        is_dxc_compatible = not is_rga_290  # 大部分编译器都兼容DXC格式

        has_dxc_params = any(param in current_params for param in ['-T ', '-E ', '-O'])
        has_rga_params = any(param in current_params for param in ['--hlsl', '--profile', '--entry'])

        should_update = False

        # 如果参数为空或者是默认参数，总是更新
        if not current_params or current_params in ['-T ps_6_0 -E main', '--hlsl --profile ps --entry main']:
            should_update = True
        # 如果编译器类型与参数格式不匹配，也要更新
        elif (is_rga_290 and has_dxc_params and not has_rga_params) or (is_dxc_compatible and has_rga_params and not has_dxc_params):
            should_update = True

        if should_update:
            # 根据编译器类型建议参数
            if is_rga_290:
                # RGA 2.9.0 需要特殊参数
                suggested_params = '--hlsl --profile ps --entry main'
                self.options_edit.setPlaceholderText('例如: --hlsl --profile ps --entry main --asic gfx1030')
            else:
                # DXC兼容编译器
                suggested_params = '-T ps_6_0 -E main'
                self.options_edit.setPlaceholderText('例如: -T ps_6_0 -E main -O2')

            self.options_edit.setText(suggested_params)

            # 如果参数格式不匹配，显示提示
            if current_params and ((is_rga_290 and has_dxc_params) or (is_dxc_compatible and has_rga_params)):
                self.options_edit.setStyleSheet('QLineEdit { background-color: #fff3cd; border: 2px solid #ffc107; }')
                # 2秒后恢复正常样式
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(2000, lambda: self.options_edit.setStyleSheet(''))
        else:
            # 更新占位符文本
            if is_rga_290:
                self.options_edit.setPlaceholderText('例如: --hlsl --profile ps --entry main --asic gfx1030')
            else:
                self.options_edit.setPlaceholderText('例如: -T ps_6_0 -E main -O2')

    def validate_compiler_options(self, compiler_name, options_text):
        """验证编译器参数是否匹配编译器类型"""
        is_rga_290 = self.is_rga_290(compiler_name)
        is_dxc_compatible = not is_rga_290  # 大部分编译器都兼容DXC格式

        # 检查参数格式
        has_dxc_params = any(param in options_text for param in ['-T ', '-E ', '-O'])
        has_rga_params = any(param in options_text for param in ['--hlsl', '--profile', '--entry'])

        error_msg = None

        if is_rga_290 and has_dxc_params and not has_rga_params:
            error_msg = f"""❌ 参数格式错误！

当前编译器: {self.combo.currentText()}
输入的参数: {options_text}

🚨 问题: 您在RGA 2.9.0编译器中使用了DXC格式的参数！

✅ 正确的RGA 2.9.0参数格式:
• 像素着色器: --hlsl --profile ps --entry main
• 顶点着色器: --hlsl --profile vs --entry main
• 计算着色器: --hlsl --profile cs --entry main

💡 提示: 点击参数输入框旁边的 "?" 按钮查看详细说明
"""

        elif is_dxc_compatible and has_rga_params and not has_dxc_params:
            error_msg = f"""❌ 参数格式错误！

当前编译器: {self.combo.currentText()}
输入的参数: {options_text}

🚨 问题: 您在DXC兼容编译器中使用了RGA 2.9.0格式的参数！

✅ 正确的DXC格式参数:
• 像素着色器: -T ps_6_0 -E main
• 顶点着色器: -T vs_6_0 -E main
• 计算着色器: -T cs_6_0 -E main

💡 提示: 点击参数输入框旁边的 "?" 按钮查看详细说明
"""

        return error_msg

    def set_compiler_list(self, compiler_list):
        """设置编译器列表到下拉框"""
        self.combo.clear()
        self.compiler_name_id_map = CompilerExplorerAPI.create_compiler_name_id_map(compiler_list)
        for name, cid in compiler_list:
            self.combo.addItem(name)

    def get_current_compiler_id(self):
        """获取当前选中的编译器ID"""
        name = self.combo.currentText()
        return self.compiler_name_id_map.get(name, '')

    def get_current_options(self):
        """获取当前的编译选项"""
        return self.options_edit.text()

    def compile_and_emit(self):
        """编译当前代码并显示结果（异步）"""
        # 如果正在编译，则忽略新的编译请求
        if self.compile_thread and self.compile_thread.isRunning():
            self._show_non_blocking_message("编译中", "编译正在进行中，请稍候...", "info")
            return

        # 获取主窗口左侧 code_area_widget 当前 tab 的文本
        main_win = qApp.activeWindow()
        if not hasattr(main_win, 'code_area_widget'):
            return
        code_widget = main_win.code_area_widget.get_current_widget()
        if code_widget is None:
            return
        source_code = code_widget.get_text()
        if not source_code:
            return

        # 获取编译器和选项
        compiler_id = self.get_current_compiler_id()
        compiler_name = self.combo.currentText().lower()
        options_text = self.get_current_options()

        # 验证参数格式是否匹配编译器类型
        validation_result = self.validate_compiler_options(compiler_name, options_text)
        if validation_result:
            self._display_result(validation_result)
            return

        options = CompilerExplorerAPI.parse_options(options_text)

        # 禁用编译按钮，显示编译中状态
        self.btn_compile.setEnabled(False)
        self.btn_compile.setText("编译中...")

        # 显示编译开始信息
        self._display_result("正在编译，请稍候...")

        # 创建工作线程
        self.compile_thread = QThread()
        self.compile_worker = CompileWorker()
        self.compile_worker.moveToThread(self.compile_thread)

        # 设置编译参数
        self.compile_worker.set_compile_params(source_code, compiler_id, options)

        # 连接信号
        self.compile_thread.started.connect(self.compile_worker.compile)
        self.compile_worker.compile_finished.connect(self._on_compile_finished)
        self.compile_worker.compile_error.connect(self._on_compile_error)
        self.compile_worker.compile_finished.connect(self.compile_thread.quit)
        self.compile_worker.compile_error.connect(self.compile_thread.quit)
        self.compile_thread.finished.connect(self._on_compile_thread_finished)

        # 启动线程
        self.compile_thread.start()

    def _on_compile_finished(self, result):
        """编译完成回调"""
        # 保存编译结果
        self.current_text = result
        # 显示结果
        self._display_result(result)

    def _on_compile_error(self, error_msg):
        """编译错误回调"""
        self._display_result(f"[编译失败] {error_msg}")

    def _on_compile_thread_finished(self):
        """编译线程结束回调"""
        # 恢复编译按钮状态
        self.btn_compile.setEnabled(True)
        self.btn_compile.setText("编译")

        # 清理线程资源
        if self.compile_thread:
            self.compile_thread.deleteLater()
            self.compile_thread = None
        if self.compile_worker:
            self.compile_worker.deleteLater()
            self.compile_worker = None

    def import_file(self):
        """导入文件到代码编辑区域"""
        # 弹出文件选择对话框
        file_dialog = QFileDialog()
        file_dialog.setAcceptMode(QFileDialog.AcceptOpen)
        file_dialog.setNameFilters([
            "所有文件 (*.*)",
            "文本文件 (*.txt)",
            "二进制文件 (*.bin)"
        ])

        if file_dialog.exec_() == QFileDialog.Accepted:
            file_path = file_dialog.selectedFiles()[0]

            try:
                # 根据文件后缀决定读取方式
                import os
                _, ext = os.path.splitext(file_path.lower())

                if ext == '.bin':
                    # 二进制文件，以十六进制+ASCII格式显示
                    self.current_text = self._read_binary_file(file_path)
                else:
                    # 其他文件都当作ASCII文本文件处理
                    self.current_text = self._read_text_file(file_path)

                # 直接显示文件内容到结果区域
                self._display_result(self.current_text)

                # 显示导入成功信息，包含文件信息
                self._show_non_blocking_message("导入成功", f"文件已成功导入：\n{file_path}", "info")

            except Exception as e:
                self._show_non_blocking_message("导入失败", f"导入文件时发生错误：\n{str(e)}", "error")

    def _read_text_file(self, file_path):
        """读取文本文件（ASCII文件）"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                raise e

        # 如果所有编码都失败，使用错误替换模式
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"无法读取文本文件: {str(e)}")

    def _read_binary_file(self, file_path):
        """读取二进制文件并转换为十六进制+ASCII显示"""
        try:
            with open(file_path, 'rb') as f:
                binary_data = f.read()

            # 转换为十六进制+ASCII显示格式
            hex_lines = []
            for i in range(0, len(binary_data), 16):
                chunk = binary_data[i:i+16]
                hex_part = ' '.join(f'{b:02x}' for b in chunk)
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
                hex_lines.append(f'{i:08x}: {hex_part:<48} {ascii_part}')

            return '\n'.join(hex_lines)

        except Exception as e:
            raise Exception(f"无法读取二进制文件: {str(e)}")

    def export_result(self):
        """导出编译结果"""
        if not self.current_text:
            self._show_non_blocking_message("导出失败", "没有可导出的编译结果！\n请先进行编译。", "warning")
            return

        # 弹出文件保存对话框
        file_dialog = QFileDialog()
        file_dialog.setAcceptMode(QFileDialog.AcceptSave)
        file_dialog.setNameFilters([
            "所有文件 (*.*)",
            "文本文件 (*.txt)",
            "HLSL着色器文件 (*.hlsl)",
            "汇编文件 (*.asm)",
            "二进制文件 (*.bin)",
            "SPIR-V二进制 (*.spv)",
            "DXBC二进制 (*.dxbc)"
        ])

        if file_dialog.exec_() == QFileDialog.Accepted:
            file_path = file_dialog.selectedFiles()[0]
            selected_filter = file_dialog.selectedNameFilter()

            try:
                # 判断是否为二进制格式
                binary_extensions = ['.bin', '.spv', '.dxbc']
                is_binary = (any(ext in selected_filter for ext in ["二进制文件", "SPIR-V二进制", "DXBC二进制"]) or
                           any(file_path.lower().endswith(ext) for ext in binary_extensions))

                if is_binary:
                    self._export_as_binary(file_path)
                else:
                    self._export_as_text(file_path)

                self._show_non_blocking_message("导出成功", f"编译结果已成功导出到：\n{file_path}", "info")

            except Exception as e:
                self._show_non_blocking_message("导出失败", f"导出文件时发生错误：\n{str(e)}", "error")

    def _export_as_text(self, file_path):
        """导出为文本文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(self.current_text)

    def _export_as_binary(self, file_path):
        """导出为二进制文件"""
        try:
            # 尝试从编译结果中提取二进制数据
            binary_data = self._extract_binary_from_result(self.current_text)

            with open(file_path, 'wb') as f:
                f.write(binary_data)

        except Exception as e:
            # 如果提取失败，直接将文本转换为二进制
            binary_data = self.current_text.encode('utf-8')
            with open(file_path, 'wb') as f:
                f.write(binary_data)

    def _extract_binary_from_result(self, result):
        """从编译结果中提取二进制数据"""
        import re
        import base64

        # 尝试查找十六进制数据
        hex_pattern = r'([0-9a-fA-F]{2}\s*){8,}'
        hex_matches = re.findall(hex_pattern, result)

        if hex_matches:
            # 提取十六进制字符串并转换为二进制
            hex_string = ''.join(re.findall(r'[0-9a-fA-F]{2}', result))
            return bytes.fromhex(hex_string)

        # 尝试查找Base64编码的数据
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        base64_matches = re.findall(base64_pattern, result)

        if base64_matches:
            # 尝试解码Base64数据
            for match in base64_matches:
                try:
                    return base64.b64decode(match)
                except:
                    continue

        # 如果没有找到特殊格式，直接转换文本
        return result.encode('utf-8')

    def fetch_compiler_list(self):
        """获取编译器列表（异步）"""
        # 如果正在获取编译器列表，则忽略新的请求
        if self.compiler_list_thread and self.compiler_list_thread.isRunning():
            return

        # 创建工作线程
        self.compiler_list_thread = QThread()
        self.compiler_list_worker = CompilerListWorker()
        self.compiler_list_worker.moveToThread(self.compiler_list_thread)

        # 连接信号
        self.compiler_list_thread.started.connect(self.compiler_list_worker.fetch_compilers)
        self.compiler_list_worker.list_finished.connect(self._on_compiler_list_finished)
        self.compiler_list_worker.list_error.connect(self._on_compiler_list_error)
        self.compiler_list_worker.list_finished.connect(self.compiler_list_thread.quit)
        self.compiler_list_worker.list_error.connect(self.compiler_list_thread.quit)
        self.compiler_list_thread.finished.connect(self._on_compiler_list_thread_finished)

        # 启动线程
        self.compiler_list_thread.start()

    def _on_compiler_list_finished(self, compiler_list):
        """编译器列表获取完成回调"""
        self.set_compiler_list(compiler_list)

    def _on_compiler_list_error(self, error_msg):
        """编译器列表获取错误回调"""
        self._show_non_blocking_message("获取编译器列表失败", error_msg, "warning")

    def _on_compiler_list_thread_finished(self):
        """编译器列表获取线程结束回调"""
        # 清理线程资源
        if self.compiler_list_thread:
            self.compiler_list_thread.deleteLater()
            self.compiler_list_thread = None
        if self.compiler_list_worker:
            self.compiler_list_worker.deleteLater()
            self.compiler_list_worker = None

    def _show_non_blocking_message(self, title, message, msg_type="info"):
        """显示非阻塞消息（在结果区域显示，而不是弹窗）"""
        if msg_type == "info":
            prefix = "[信息]"
        elif msg_type == "warning":
            prefix = "[警告]"
        elif msg_type == "error":
            prefix = "[错误]"
        else:
            prefix = "[消息]"

        formatted_message = f"{prefix} {title}\n{message}"
        self._display_result(formatted_message)

    def _display_result(self, result):
        """显示编译结果到文本区域"""
        container = self.parent()
        if container is not None and container.layout() is not None and container.layout().count() > 1:
            text_widget = container.layout().itemAt(1).widget()
            if hasattr(text_widget, 'set_text'):
                text_widget.set_text(result)
