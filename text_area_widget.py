from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget, QLabel, QHBoxLayout, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QPainter, QColor
import os

from ui.DisplayWidget.code_display_widget_qscintilla import CodeDisplayWidget as QScintillaWidget
from ui.DisplayWidget.code_display_widget_pygments import CodeDisplayWidget as PygmentsWidget
from ui.DisplayWidget.text_display_widget import TextDisplayWidget as OriginalWidget

class TextAreaWidget(QWidget):

    def __init__(self, parent=None, is_compile=False):
        self.is_compile = is_compile
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        self.layout.addWidget(self.tab_widget)

        # 创建默认widget（只创建一次，重复使用）
        self.default_widget = self._create_default_widget()

        # 存储widget到tab索引的映射，用于更新tab状态
        self.widget_to_tab_index = {}

    def _create_default_widget(self):
        widget = QScintillaWidget(model='QScintilla', source=None)
        widget.set_text('请先导入文件再执行编译' if self.is_compile else '请导入文件')

        # 如果是编译模式，设置为只读
        if self.is_compile:
            widget.set_editable(False)
            container = QWidget()
            vlayout = QVBoxLayout(container)
            vlayout.setContentsMargins(0, 0, 0, 0)
            vlayout.setSpacing(0)
            from ui.compiler_explorer_panel import CompilerExplorerPanel
            compiler_panel = CompilerExplorerPanel()
            vlayout.addWidget(compiler_panel)
            vlayout.addWidget(widget, 1)
            widget = container
        self.tab_widget.addTab(widget, '编译' if self.is_compile else '代码')
        return widget

    def _process_default_tab(self):
        if self.tab_widget.count() == 0:
            self.tab_widget.addTab(self.default_widget, '编译' if self.is_compile else '代码')
        elif self.get_current_widget() is self.default_widget and self.is_compile == False:
            self.tab_widget.removeTab(0)
    

    def add_code_page(self, label: str, content: str = "", source: str = None, model: str = 'QScintilla'):
        """添加代码页面"""
        # 创建新的widget
        widget = None
        if model == 'QScintilla' and QScintillaWidget is not None:
            widget = QScintillaWidget(model='QScintilla', source=source)
        elif model == 'Pygments' and PygmentsWidget is not None:
            widget = PygmentsWidget(model='Pygments', source=source)
        elif model == 'Original' or model is None:
            widget = OriginalWidget(model='Original', source=source)
        else:
            widget = OriginalWidget(model='Original', source=source)

        widget.set_text(content)

        # 如果是编译模式，设置为只读并添加编译面板
        if self.is_compile:
            widget.set_editable(False)
            container = QWidget()
            vlayout = QVBoxLayout(container)
            vlayout.setContentsMargins(0, 0, 0, 0)
            vlayout.setSpacing(0)
            from ui.compiler_explorer_panel import CompilerExplorerPanel
            compiler_panel = CompilerExplorerPanel()
            vlayout.addWidget(compiler_panel)
            vlayout.addWidget(widget, 1)
            widget = container

        # 添加tab
        self._process_default_tab()
        tab_index = self.tab_widget.addTab(widget, label)

        # 自动切换到新添加的tab
        self.tab_widget.setCurrentIndex(tab_index)

        # 连接修改状态信号（如果不是编译模式）
        if not self.is_compile:
            actual_widget = self._get_actual_code_widget(widget)
            self._setup_widget_connections(actual_widget, tab_index)

        return widget

    def set_current_index(self, idx: int):
        self.tab_widget.setCurrentIndex(idx)

    def get_current_widget(self):
        idx = self.tab_widget.currentIndex()
        if 0 <= idx < self.tab_widget.count():
            return self.tab_widget.widget(idx)
        return None
    
    def get_current_text(self):
        widget = self.get_current_widget()
        if(widget and widget is not self.default_widget):
            return widget.get_text()
        return ''

    def _handle_save_dialog(self, actual_widget, tab_title: str) -> bool:
        """处理保存对话框，返回是否应该继续关闭操作"""
        reply = QMessageBox.question(
            self, 
            '保存提醒', 
            f'文件 "{tab_title}" 已修改，是否保存？',
            QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
            QMessageBox.Save
        )
        
        if reply == QMessageBox.Save:
            # 用户选择保存
            if hasattr(actual_widget, 'save_file'):
                if not actual_widget.save_file():
                    # 如果保存失败，取消关闭
                    return False
            else:
                # 如果没有save_file方法，尝试另存为
                if hasattr(actual_widget, 'save_as_file'):
                    if not actual_widget.save_as_file():
                        # 如果另存为失败，取消关闭
                        return False
                else:
                    # 如果都没有保存方法，取消关闭
                    QMessageBox.warning(self, '保存失败', '无法保存文件，取消关闭操作')
                    return False
        elif reply == QMessageBox.Cancel:
            # 用户选择取消
            return False
        # 如果用户选择Discard，继续执行关闭操作
        return True
    
    def _cleanup_tab_mappings(self, idx: int):
        """清理tab映射关系"""
        # 清理映射关系
        actual_widget = self._get_actual_code_widget(self.tab_widget.widget(idx))
        if actual_widget and actual_widget in self.widget_to_tab_index:
            del self.widget_to_tab_index[actual_widget]

        # 更新其他tab的索引映射
        for code_widget, tab_index in list(self.widget_to_tab_index.items()):
            if tab_index > idx:
                self.widget_to_tab_index[code_widget] = tab_index - 1
    
    def _setup_widget_connections(self, actual_widget, tab_index: int):
        """设置widget的信号连接"""
        if not actual_widget:
            return
            
        # 存储映射关系
        self.widget_to_tab_index[actual_widget] = tab_index

        # 连接保存信号
        if hasattr(actual_widget, 'file_saved'):
            actual_widget.file_saved.connect(lambda path: self._on_file_saved(actual_widget))

        # 连接修改状态变化信号
        if hasattr(actual_widget, 'modified_changed'):
            actual_widget.modified_changed.connect(lambda modified: self._update_tab_modified_state(actual_widget, modified))
        
        # 连接文本变化信号以更新历史记录
        if hasattr(actual_widget, 'text') and hasattr(actual_widget.text, 'textChanged'):
            # 对于QScintilla组件，延迟添加到历史记录
            def on_text_changed():
                # 延迟执行，避免在每个字符输入时都添加历史记录
                QTimer.singleShot(1000, lambda: actual_widget.add_to_history())
            
            actual_widget.text.textChanged.connect(on_text_changed)
    
    def close_tab(self, idx):
        """关闭指定索引的tab"""
        if 0 <= idx < self.tab_widget.count():
            widget = self.tab_widget.widget(idx)

            # 不能关闭默认tab
            if widget is self.default_widget:
                return

            # 检查是否有未保存的修改
            actual_widget = self._get_actual_code_widget(widget)
            if actual_widget and hasattr(actual_widget, 'is_modified') and actual_widget.is_modified:
                # 移除修改标记（●）以获取原始标题
                tab_title = self.tab_widget.tabText(idx)
                if tab_title.startswith('● '):
                    tab_title = tab_title[2:]
                
                # 处理保存对话框
                if not self._handle_save_dialog(actual_widget, tab_title):
                    return

            # 清理映射关系
            self._cleanup_tab_mappings(idx)

            # 移除tab
            self.tab_widget.removeTab(idx)
            if widget:
                widget.deleteLater()

        self._process_default_tab()

    def _get_actual_code_widget(self, widget):
        """获取实际的代码显示widget（处理容器widget的情况）"""
        # 首先检查是否直接是代码显示widget
        if hasattr(widget, 'get_text') and hasattr(widget, 'set_text'):
            return widget

        # 如果是容器widget，查找其中的代码显示widget
        if hasattr(widget, 'layout') and widget.layout() is not None:
            for i in range(widget.layout().count()):
                item = widget.layout().itemAt(i)
                if item and item.widget():
                    child_widget = item.widget()
                    if hasattr(child_widget, 'get_text') and hasattr(child_widget, 'set_text'):
                        return child_widget

        return None

    def _update_tab_modified_state(self, code_widget, is_modified):
        """更新tab的修改状态显示"""
        if code_widget in self.widget_to_tab_index:
            tab_index = self.widget_to_tab_index[code_widget]

            if 0 <= tab_index < self.tab_widget.count():
                current_text = self.tab_widget.tabText(tab_index)

                if is_modified:
                    # 添加修改标记（白色圆点）
                    if not current_text.startswith('● '):
                        new_text = f'● {current_text}'
                        self.tab_widget.setTabText(tab_index, new_text)
                else:
                    # 移除修改标记
                    if current_text.startswith('● '):
                        new_text = current_text[2:]
                        self.tab_widget.setTabText(tab_index, new_text)

    def _on_file_saved(self, code_widget):
        """文件保存后的处理"""
        self._update_tab_modified_state(code_widget, False)
