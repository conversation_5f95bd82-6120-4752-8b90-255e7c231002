from PyQt5.QtWidgets import <PERSON><PERSON>oxLayout, QHBoxLayout, QLabel, QComboBox
from ui.Process.process_widget_base import ProcessWidgetBase
from Process.legacy_optimizers.shader_auto_optimize import ShaderAutoOptimizer
from Process.legacy_optimizers.process_half_float_convert import HalfFloatConverter

class WidgetLegacyOptimizers(ProcessWidgetBase):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.label.setText('传统优化器')
        # 功能说明映射
        self.info_map = {
            'shader_auto_optimize': '通用自动优化器\n• 通用代码优化\n• 性能提升\n• 兼容性保证\n• 自动化处理\n\n这是项目原有的优化器，提供基础的通用优化功能。',
            'half_float_convert': 'Half/Float类型转换器\n• half ↔ float 双向转换\n• 智能类型识别\n• 多种转换模式\n• 精度控制\n\n支持全局、局部变量、函数参数等多种转换方式。'
        }
        # 优化器选择
        optimizer_layout = QHBoxLayout()
        optimizer_label = QLabel('优化器:')
        self.optimizer_combo = QComboBox()
        self.optimizer_combo.addItem('通用自动优化器', 'shader_auto_optimize')
        self.optimizer_combo.addItem('Half/Float类型转换器', 'half_float_convert')
        self.optimizer_combo.currentTextChanged.connect(self.on_optimizer_changed)
        optimizer_layout.addWidget(optimizer_label)
        optimizer_layout.addWidget(self.optimizer_combo)
        
        # 参数容器
        self.params_layout = QVBoxLayout()
        
        # 功能说明标签
        self.info_label = QLabel()
        self.info_label.setStyleSheet('color: gray; font-size: 10px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;')
        self.info_label.setWordWrap(True)
        
        # 插入到主布局
        self.layout.insertLayout(1, optimizer_layout)
        self.layout.insertLayout(2, self.params_layout)
        self.layout.insertWidget(3, self.info_label)
        
        # 初始化显示
        self.on_optimizer_changed()
        


    def clear_params_layout(self):
        """清空参数布局"""
        def clear_layout(layout):
            while layout.count():
                child = layout.takeAt(0)
                if child is None:
                    continue

                widget = child.widget()
                if widget is not None:
                    widget.setParent(None)
                    widget.deleteLater()

                child_layout = child.layout()
                if child_layout is not None:
                    clear_layout(child_layout)
                    child_layout.deleteLater()

        clear_layout(self.params_layout)

        # 强制处理待删除的对象
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

    def setup_half_float_params(self):
        """设置Half/Float转换参数"""
        # 转换方向
        dir_layout = QHBoxLayout()
        dir_label = QLabel('转换方向:')
        self.dir_combo = QComboBox()
        self.dir_combo.addItem('half → float', HalfFloatConverter.DIRECTION_HALF2FLOAT)
        self.dir_combo.addItem('float → half', HalfFloatConverter.DIRECTION_FLOAT2HALF)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_combo)
        
        # 转换方式
        method_layout = QHBoxLayout()
        method_label = QLabel('转换方式:')
        self.method_combo = QComboBox()
        self.method_combo.addItem('全局转换', HalfFloatConverter.METHOD_GLOBAL)
        self.method_combo.addItem('只转换局部变量', HalfFloatConverter.METHOD_LOCAL)
        self.method_combo.addItem('只转换全局变量', HalfFloatConverter.METHOD_GLOBAL_VAR)
        self.method_combo.addItem('只转换函数参数', HalfFloatConverter.METHOD_PARAM)
        self.method_combo.addItem('只转换函数返回值', HalfFloatConverter.METHOD_RETURN)
        self.method_combo.addItem('只转换带注释的', HalfFloatConverter.METHOD_ANNOTATED)
        method_layout.addWidget(method_label)
        method_layout.addWidget(self.method_combo)
        
        self.params_layout.addLayout(dir_layout)
        self.params_layout.addLayout(method_layout)

    def on_optimizer_changed(self):
        """当优化器选择改变时更新参数和说明"""
        self.clear_params_layout()

        optimizer_key = self.optimizer_combo.currentData()

        # 设置参数界面
        if optimizer_key == 'half_float_convert':
            self.setup_half_float_params()
        # shader_auto_optimize 不需要额外参数

        # 强制刷新布局
        self.params_layout.update()
        self.layout.update()

        # 更新说明
        if optimizer_key in self.info_map:
            self.info_label.setText(self.info_map[optimizer_key])

    def run_algorithm(self, text):
        optimizer_key = self.optimizer_combo.currentData()
        
        try:
            if optimizer_key == 'shader_auto_optimize':
                optimizer = ShaderAutoOptimizer()
                return optimizer.optimize_code(text)
                
            elif optimizer_key == 'half_float_convert':
                direction = self.dir_combo.currentData()
                method = self.method_combo.currentData()
                optimizer = HalfFloatConverter()
                return optimizer.optimize_code(text, direction, method)
            
            else:
                return "错误：未知的优化器类型"
                
        except Exception as e:
            return f"优化失败：{str(e)}"
