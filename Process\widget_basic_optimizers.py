from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
                             QCheckBox, QGroupBox, QPushButton, QMessageBox,
                             QScrollArea, QWidget, QSizePolicy)
from PyQt5.QtCore import Qt
from ui.Process.process_widget_base import ProcessWidgetBase
from Process.basic_optimizers import (
    OptimizationLevel, HLSLOptimizerManager, GLSLOptimizerManager
)

class WidgetBasicOptimizers(ProcessWidgetBase):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.label.setText('基础优化器')

        # 初始化管理器
        self.hlsl_manager = None
        self.glsl_manager = None
        self.current_manager = None

        # 优化器复选框字典
        self.optimizer_checkboxes = {}

        # 优化器说明映射
        self.optimizer_descriptions = {
            # HLSL优化器说明
            'hlsl': {
                'texture_sampling': 'HLSL纹理采样优化器\n• 采样器状态合并\n• LOD计算优化\n• 重复采样消除\n• 向量化采样操作',
                'loop_unroll': 'HLSL循环展开优化器\n• 固定次数循环展开\n• 嵌套循环优化\n• 循环不变量提取\n• 添加[unroll]属性',
                'branch': 'HLSL分支优化器\n• 分支消除\n• 布尔表达式简化\n• 条件合并\n• 无分支代码转换',
                'memory_access': 'HLSL内存访问优化器\n• 向量化内存加载\n• 缓存友好访问\n• 常量缓冲区优化\n• 共享内存优化',
                'math_function': 'HLSL数学函数优化器\n• 幂函数优化 (pow→乘法)\n• 平方根函数优化\n• 常量折叠\n• 快速近似算法',
                'vectorization': 'HLSL向量化优化器\n• 标量运算向量化\n• 数组访问向量化\n• 函数调用向量化\n• SIMD指令生成',
                'precision_analyzer': 'HLSL精度分析优化器\n• 精度需求分析\n• 类型降级建议\n• 数值稳定性检查\n• 精度损失评估',
                'register_allocation': 'HLSL寄存器分配优化器\n• 变量生命周期分析\n• 寄存器复用优化\n• 临时变量消除\n• 寄存器压力分析'
            },
            # GLSL优化器说明
            'glsl': {
                'texture_sampling': 'GLSL纹理采样优化器\n• 纹理函数优化\n• LOD计算优化\n• 重复采样消除\n• 精度优化',
                'loop_unroll': 'GLSL循环展开优化器\n• 固定次数循环展开\n• 嵌套循环优化\n• 循环不变量提取\n• 动态分支优化',
                'branch': 'GLSL分支优化器\n• 分支消除 (使用mix)\n• 布尔表达式简化\n• 向量条件优化\n• 分支扁平化',
                'memory_access': 'GLSL内存访问优化器\n• uniform访问优化\n• 向量化内存加载\n• 内存访问合并\n• 变量生命周期优化',
                'math_function': 'GLSL数学函数优化器\n• 幂函数优化 (pow→乘法)\n• 平方根函数优化\n• 常量折叠\n• 数学恒等式应用',
                'vectorization': 'GLSL向量化优化器\n• 标量运算向量化\n• swizzle操作优化\n• 向量构造优化\n• 分量赋值合并',
                'precision_analyzer': 'GLSL精度分析优化器\n• 精度限定符优化\n• 精度降级 (highp→mediump→lowp)\n• 移动端GPU优化\n• 精度传播分析',
                'register_allocation': 'GLSL寄存器分配优化器\n• 变量生命周期分析\n• 寄存器重用优化\n• 临时变量消除\n• 寄存器压力分析'
            }
        }
        # 着色器语言选择
        language_layout = QHBoxLayout()
        language_label = QLabel('着色器语言:')
        self.language_combo = QComboBox()
        self.language_combo.addItem('HLSL (DirectX)', 'hlsl')
        self.language_combo.addItem('GLSL (OpenGL)', 'glsl')
        self.language_combo.currentTextChanged.connect(self.on_language_changed)

        # 帮助按钮
        self.help_button = QPushButton('?')
        self.help_button.setFixedSize(25, 25)
        self.help_button.setStyleSheet('QPushButton { font-weight: bold; }')
        self.help_button.clicked.connect(self.show_help_dialog)

        language_layout.addWidget(language_label)
        language_layout.addWidget(self.language_combo)
        language_layout.addStretch()
        language_layout.addWidget(self.help_button)

        # 优化级别选择
        level_layout = QHBoxLayout()
        level_label = QLabel('优化级别:')
        self.level_combo = QComboBox()
        self.level_combo.addItem('基础优化', OptimizationLevel.BASIC)
        self.level_combo.addItem('激进优化', OptimizationLevel.AGGRESSIVE)
        self.level_combo.addItem('最大优化', OptimizationLevel.MAXIMUM)
        level_layout.addWidget(level_label)
        level_layout.addWidget(self.level_combo)
        level_layout.addStretch()

        # 优化器选择区域（滚动区域）
        self.optimizer_group = QGroupBox('优化器选择')
        self.optimizer_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)

        self.optimizer_scroll = QScrollArea()
        self.optimizer_scroll.setWidgetResizable(True)
        self.optimizer_scroll.setMaximumHeight(200)
        self.optimizer_scroll.setMinimumHeight(150)

        self.optimizer_widget = QWidget()
        self.optimizer_layout = QVBoxLayout(self.optimizer_widget)
        self.optimizer_layout.setSpacing(2)  # 减少间距
        self.optimizer_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        self.optimizer_scroll.setWidget(self.optimizer_widget)

        group_layout = QVBoxLayout(self.optimizer_group)
        group_layout.setContentsMargins(5, 15, 5, 5)  # 减少边距
        group_layout.addWidget(self.optimizer_scroll)

        # 插入到主布局
        self.layout.insertLayout(1, language_layout)
        self.layout.insertLayout(2, level_layout)
        self.layout.insertWidget(3, self.optimizer_group)

        # 添加拉伸项，确保组件紧凑排列在顶部
        self.layout.addStretch()

        # 设置主布局的间距和边距
        self.layout.setSpacing(8)  # 减少组件间距
        self.layout.setContentsMargins(10, 10, 10, 10)  # 设置合适的边距

        # 初始化显示
        self.on_language_changed()

    def on_language_changed(self):
        """当着色器语言选择改变时更新优化器列表"""
        language = self.language_combo.currentData()

        # 清除现有的复选框
        for checkbox in self.optimizer_checkboxes.values():
            checkbox.setParent(None)
        self.optimizer_checkboxes.clear()

        # 初始化对应的管理器
        level = self.level_combo.currentData()
        if language == 'hlsl':
            self.hlsl_manager = HLSLOptimizerManager(level)
            self.current_manager = self.hlsl_manager
            optimizers = self.hlsl_manager.get_available_optimizers()
        else:  # glsl
            self.glsl_manager = GLSLOptimizerManager(level)
            self.current_manager = self.glsl_manager
            optimizers = self.glsl_manager.get_available_optimizers()

        # 创建优化器复选框
        for optimizer_name in optimizers:
            checkbox = QCheckBox(self.get_optimizer_display_name(optimizer_name, language))
            checkbox.setToolTip(self.get_optimizer_description(optimizer_name, language))
            checkbox.setChecked(True)  # 默认全选
            self.optimizer_checkboxes[optimizer_name] = checkbox
            self.optimizer_layout.addWidget(checkbox)

    def get_optimizer_display_name(self, optimizer_name, language):
        """获取优化器显示名称"""
        name_map = {
            'texture_sampling': '纹理采样优化器',
            'loop_unroll': '循环展开优化器',
            'branch': '分支优化器',
            'memory_access': '内存访问优化器',
            'math_function': '数学函数优化器',
            'vectorization': '向量化优化器',
            'precision_analyzer': '精度分析优化器',
            'register_allocation': '寄存器分配优化器'
        }
        return name_map.get(optimizer_name, optimizer_name)

    def get_optimizer_description(self, optimizer_name, language):
        """获取优化器描述"""
        if language in self.optimizer_descriptions:
            return self.optimizer_descriptions[language].get(optimizer_name, '暂无描述')
        return '暂无描述'

    def show_help_dialog(self):
        """显示帮助对话框"""
        language = self.language_combo.currentData()

        if language == 'hlsl':
            title = "HLSL (High-Level Shading Language) 优化器说明"
            content = """
<h3>HLSL 着色器语言</h3>
<p><b>HLSL</b> 是微软开发的高级着色器语言，主要用于 DirectX 图形API。</p>

<h4>特点：</h4>
<ul>
<li><b>DirectX 兼容</b>：专为 DirectX 11/12 设计</li>
<li><b>丰富的数据类型</b>：支持 float、half、min16float 等精度类型</li>
<li><b>强大的内置函数</b>：提供丰富的数学和纹理函数</li>
<li><b>寄存器优化</b>：支持显式寄存器分配</li>
<li><b>类型转换</b>：支持 float ↔ half 精度转换优化</li>
</ul>

<h4>适用场景：</h4>
<ul>
<li>Windows 平台游戏开发</li>
<li>DirectX 应用程序</li>
<li>Xbox 游戏开发</li>
<li>高性能图形应用</li>
</ul>

<h4>优化器特色：</h4>
<ul>
<li><b>寄存器分配</b>：针对 DirectX 寄存器模型优化</li>
<li><b>精度分析</b>：智能分析变量精度需求</li>
<li><b>专业优化</b>：8个专门的优化器覆盖各个方面</li>
</ul>

<h4>8个HLSL优化器详细介绍：</h4>
<ul>
<li><b>纹理采样优化器</b>：优化纹理采样操作，包括采样器状态合并、LOD计算优化、重复采样消除、向量化采样操作</li>
<li><b>循环展开优化器</b>：智能分析和展开循环结构，包括固定次数循环展开、嵌套循环优化、循环不变量提取、添加[unroll]属性</li>
<li><b>分支优化器</b>：优化条件分支结构，包括分支消除、布尔表达式简化、条件合并、无分支代码转换</li>
<li><b>内存访问优化器</b>：优化内存访问模式，包括向量化内存加载、缓存友好访问、常量缓冲区优化、共享内存优化</li>
<li><b>数学函数优化器</b>：优化数学运算和函数调用，包括幂函数优化(pow→乘法)、平方根函数优化、常量折叠、快速近似算法</li>
<li><b>向量化优化器</b>：将标量操作转换为向量操作，包括标量运算向量化、数组访问向量化、函数调用向量化、SIMD指令生成</li>
<li><b>精度分析优化器</b>：智能分析变量精度需求，包括精度需求分析、类型降级建议、数值稳定性检查、精度损失评估</li>
<li><b>寄存器分配优化器</b>：优化变量的寄存器使用，包括变量生命周期分析、寄存器复用优化、临时变量消除、寄存器压力分析</li>
</ul>
            """
        else:  # glsl
            title = "GLSL (OpenGL Shading Language) 优化器说明"
            content = """
<h3>GLSL 着色器语言</h3>
<p><b>GLSL</b> 是 OpenGL 的官方着色器语言，广泛用于跨平台图形开发。</p>

<h4>特点：</h4>
<ul>
<li><b>跨平台兼容</b>：支持 Windows、Linux、macOS、移动平台</li>
<li><b>精度限定符</b>：支持 highp、mediump、lowp 精度控制</li>
<li><b>Swizzle 操作</b>：强大的向量分量操作 (.xyz, .rgb 等)</li>
<li><b>内置变量</b>：丰富的着色器阶段内置变量</li>
<li><b>移动端优化</b>：特别适合移动端 GPU 优化</li>
</ul>

<h4>适用场景：</h4>
<ul>
<li>跨平台游戏开发</li>
<li>移动端应用开发</li>
<li>Web 图形应用 (WebGL)</li>
<li>科学可视化</li>
</ul>

<h4>优化器特色：</h4>
<ul>
<li><b>精度分析优化器</b>：智能选择精度限定符，优化移动端性能</li>
<li><b>Swizzle 优化</b>：优化向量分量操作</li>
<li><b>分支优化</b>：使用 mix() 等 GLSL 特有函数优化分支</li>
<li><b>着色器阶段检测</b>：自动检测顶点/片段/计算着色器</li>
</ul>

<h4>8个GLSL优化器详细介绍：</h4>
<ul>
<li><b>纹理采样优化器</b>：优化GLSL纹理采样操作，包括纹理函数优化、LOD计算优化、重复采样消除、精度优化</li>
<li><b>循环展开优化器</b>：智能分析和展开GLSL循环结构，包括固定次数循环展开、嵌套循环优化、循环不变量提取、动态分支优化</li>
<li><b>分支优化器</b>：优化GLSL条件分支，包括分支消除(使用mix)、布尔表达式简化、向量条件优化、分支扁平化</li>
<li><b>内存访问优化器</b>：优化GLSL内存访问模式，包括uniform访问优化、向量化内存加载、内存访问合并、变量生命周期优化</li>
<li><b>数学函数优化器</b>：优化GLSL数学运算和函数调用，包括幂函数优化(pow→乘法)、平方根函数优化、常量折叠、数学恒等式应用</li>
<li><b>向量化优化器</b>：将标量操作转换为向量操作，包括标量运算向量化、swizzle操作优化、向量构造优化、分量赋值合并</li>
<li><b>精度分析优化器</b>：智能分析精度需求，包括精度限定符优化、精度降级(highp→mediump→lowp)、移动端GPU优化、精度传播分析</li>
<li><b>寄存器分配优化器</b>：优化变量的寄存器使用，包括变量生命周期分析、寄存器重用优化、临时变量消除、寄存器压力分析</li>
</ul>
            """

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(content)
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setStandardButtons(QMessageBox.Ok)

        # 设置为非模态对话框，不阻塞主窗口
        msg_box.setModal(False)
        msg_box.show()  # 使用show()而不是exec_()来实现异步

    def run_algorithm(self, text):
        """运行选中的优化器"""
        if not self.current_manager:
            return "错误：未初始化优化器管理器"

        # 获取选中的优化器
        selected_optimizers = []
        for optimizer_name, checkbox in self.optimizer_checkboxes.items():
            if checkbox.isChecked():
                selected_optimizers.append(optimizer_name)

        if not selected_optimizers:
            return "错误：请至少选择一个优化器"

        # 更新优化级别
        level = self.level_combo.currentData()
        self.current_manager.set_optimization_level(level)

        try:
            # 执行优化
            result = self.current_manager.optimize_all(text, selected_optimizers)

            # 获取统计信息
            stats = self.current_manager.get_total_statistics()
            stats_text = f"\n\n// 优化统计信息:\n"
            stats_text += f"// 应用的优化次数: {stats['total_optimizations_applied']}\n"
            stats_text += f"// 处理的代码行数: {stats['total_lines_processed']}\n"
            stats_text += f"// 使用的优化器数量: {stats['optimizers_used']}\n"

            return result + stats_text

        except Exception as e:
            return f"错误：优化过程中发生异常 - {str(e)}"
