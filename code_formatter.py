﻿"""
HLSL/GLSL代码格式化器模块
专门用于着色器语言的格式化功能
"""
import re
import subprocess
import tempfile
import os
from typing import Optional
from enum import Enum

class CodeType(Enum):
    HLSL = "hlsl"
    GLSL = "glsl"
    UNKNOWN = "unknown"

class ShaderFormatter:
    """专门用于着色器语言的格式化器"""
    
    def __init__(self):
        # 定义可能的clang-format路径
        self.possible_clang_format_paths = [
            'clang-format',  # 如果在PATH中
            r'D:\Program Files\LLVM\bin\clang-format.exe',  # 用户安装的路径
            r'C:\Program Files\LLVM\bin\clang-format.exe',
            r'C:\Program Files (x86)\LLVM\bin\clang-format.exe',
        ]
        
        self.hlsl_keywords = {
            'float', 'float2', 'float3', 'float4', 'float4x4', 'float3x3', 'float2x2',
            'int', 'int2', 'int3', 'int4', 'uint', 'uint2', 'uint3', 'uint4',
            'bool', 'bool2', 'bool3', 'bool4', 'half', 'half2', 'half3', 'half4',
            'double', 'double2', 'double3', 'double4',
            'Texture2D', 'TextureCube', 'Texture3D', 'Texture2DArray',
            'SamplerState', 'SamplerComparisonState',
            'struct', 'cbuffer', 'register', 'packoffset',
            'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default',
            'return', 'break', 'continue', 'discard',
            'void', 'const', 'static', 'uniform', 'extern',
            'in', 'out', 'inout', 'nointerpolation', 'linear', 'centroid',
            'vertex', 'pixel', 'geometry', 'hull', 'domain', 'compute'
        }
        
        self.glsl_keywords = {
            'float', 'vec2', 'vec3', 'vec4', 'mat2', 'mat3', 'mat4',
            'int', 'ivec2', 'ivec3', 'ivec4', 'uint', 'uvec2', 'uvec3', 'uvec4',
            'bool', 'bvec2', 'bvec3', 'bvec4',
            'sampler2D', 'samplerCube', 'sampler3D', 'sampler2DArray',
            'texture', 'uniform', 'attribute', 'varying',
            'struct', 'layout', 'location', 'binding',
            'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default',
            'return', 'break', 'continue', 'discard',
            'void', 'const', 'in', 'out', 'inout',
            'vertex', 'fragment', 'geometry', 'compute'
        }
    
    def _test_clang_format_path(self, path: str) -> bool:
        """测试clang-format路径是否可用"""
        try:
            result = subprocess.run([path, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def get_clang_format_path(self) -> Optional[str]:
        """获取clang-format的完整路径"""
        for path in self.possible_clang_format_paths:
            if self._test_clang_format_path(path):
                return path
        return None
    
    def check_clang_format_available(self) -> bool:
        """检查clang-format是否可用"""
        return self.get_clang_format_path() is not None
    
    def detect_code_type(self, code: str) -> CodeType:
        """检测代码类型（HLSL或GLSL）"""
        hlsl_score = 0
        glsl_score = 0
        
        # 检查HLSL特有的特征
        hlsl_patterns = [
            r'\bfloat[234]?\b', r'\bTexture2D\b', r'\bSamplerState\b',
            r'\bcbuffer\b', r'\bregister\b', r'\bpackoffset\b',
            r'\bSV_Position\b', r'\bSV_Target\b', r'\bSV_VertexID\b'
        ]
        
        # 检查GLSL特有的特征
        glsl_patterns = [
            r'\bvec[234]?\b', r'\bmat[234]?\b', r'\bsampler2D\b',
            r'\buniform\b', r'\battribute\b', r'\bvarying\b',
            r'\blayout\b', r'\bgl_Position\b', r'\bgl_FragColor\b'
        ]
        
        for pattern in hlsl_patterns:
            if re.search(pattern, code):
                hlsl_score += 1
                
        for pattern in glsl_patterns:
            if re.search(pattern, code):
                glsl_score += 1
        
        if hlsl_score > glsl_score:
            return CodeType.HLSL
        elif glsl_score > hlsl_score:
            return CodeType.GLSL
        else:
            return CodeType.UNKNOWN
    
    def format_with_clang_format(self, code: str, code_type: CodeType) -> Optional[str]:
        """使用clang-format格式化shader代码"""
        clang_format_path = self.get_clang_format_path()
        if not clang_format_path:
            return None
            
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.hlsl' if code_type == CodeType.HLSL else '.glsl', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # 查找项目根目录的.clang-format配置文件
                config_file = self._find_clang_format_config()
                
                # 构建clang-format命令
                if config_file:
                    cmd = [clang_format_path, f'-style=file:{config_file}', temp_file]
                else:
                    cmd = [clang_format_path, '-style=file', temp_file]
                
                # 尝试运行clang-format
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    return result.stdout
                else:
                    print(f"clang-format失败: {result.stderr}")
                    return None
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    
        except Exception as e:
            print(f"clang-format格式化失败: {e}")
            return None
    
    def _find_clang_format_config(self) -> Optional[str]:
        """查找.clang-format配置文件"""
        # 从当前目录开始向上查找
        current_dir = os.getcwd()
        
        # 检查当前目录
        config_path = os.path.join(current_dir, '.clang-format')
        if os.path.exists(config_path):
            return config_path
        
        # 检查上级目录（项目根目录）
        parent_dir = os.path.dirname(current_dir)
        config_path = os.path.join(parent_dir, '.clang-format')
        if os.path.exists(config_path):
            return config_path
        
        # 检查shaderProcess目录
        shader_process_dir = os.path.join(current_dir, 'shaderProcess')
        if os.path.exists(shader_process_dir):
            config_path = os.path.join(shader_process_dir, '.clang-format')
            if os.path.exists(config_path):
                return config_path
        
        return None
    
    def format_shader_code(self, code: str, indent_size: int = 4) -> str:
        """
        格式化shader代码
        
        Args:
            code: 要格式化的代码
            indent_size: 缩进大小，默认4个空格
            
        Returns:
            格式化后的代码
        """
        if not code.strip():
            return code
            
        # 检测代码类型
        code_type = self.detect_code_type(code)
        
        # 优先使用clang-format
        formatted = self.format_with_clang_format(code, code_type)
        if formatted is not None:
            return formatted
        
        # 如果clang-format不可用，使用内置格式化器
        return self._format_with_builtin(code, indent_size, code_type)
    
    def _format_with_builtin(self, code: str, indent_size: int, code_type: CodeType) -> str:
        """使用内置格式化器"""
        # 预处理：移除多余的空白字符
        lines = code.split('\n')
        processed_lines = []
        
        for line in lines:
            # 移除行首行尾空白
            line = line.rstrip()
            if line.strip():  # 非空行
                processed_lines.append(line)
            else:
                processed_lines.append('')  # 保留空行
        
        # 格式化代码
        formatted_lines = []
        indent_level = 0
        in_multiline_comment = False
        
        for i, line in enumerate(processed_lines):
            if not line.strip():
                formatted_lines.append('')
                continue
            
            # 处理多行注释
            stripped_line = line.strip()
            if '/*' in stripped_line and '*/' not in stripped_line:
                in_multiline_comment = True
            elif '*/' in stripped_line:
                in_multiline_comment = False
            
            if in_multiline_comment:
                # 多行注释中保持原样
                formatted_lines.append(line)
                continue
            
            # 计算缩进级别
            new_indent_level = self._calculate_indent_level(stripped_line, indent_level, code_type)
            
            # 格式化当前行
            formatted_line = self._format_line(line, new_indent_level, indent_size, code_type)
            formatted_lines.append(formatted_line)
            
            # 更新缩进级别
            indent_level = new_indent_level
        
        return '\n'.join(formatted_lines)
    
    def _format_line(self, line: str, indent_level: int, indent_size: int, code_type: CodeType) -> str:
        """格式化单行代码"""
        # 添加缩进
        indent = ' ' * (indent_level * indent_size)
        
        # 移除行首空白
        line = line.lstrip()
        
        # 针对shader语言的格式化
        line = self._format_shader_syntax(line, code_type)
        
        return indent + line
    
    def _format_shader_syntax(self, line: str, code_type: CodeType) -> str:
        """针对shader语言的语法格式化"""
        # 避免在字符串中修改
        if '"' in line or "'" in line:
            return line
        
        # 在特定操作符前后添加空格
        operators = ['==', '!=', '<=', '>=', '&&', '||', '+=', '-=', '*=', '/=']
        
        for op in operators:
            # 只在操作符前后都有非空白字符时添加空格
            pattern = r'([^\s])' + re.escape(op) + r'([^\s])'
            replacement = r'\1 ' + op + r' \2'
            line = re.sub(pattern, replacement, line)
        
        # 在逗号后添加空格
        line = re.sub(r',([^\s])', r', \1', line)
        
        # 在分号前移除多余空格
        line = re.sub(r'\s+;', ';', line)
        
        # 在函数调用的括号前添加空格
        line = re.sub(r'(\w)\s*\(', r'\1(', line)
        
        # 清理多余的空格
        line = re.sub(r'\s+', ' ', line)
        return line.strip()
    
    def _calculate_indent_level(self, line: str, current_level: int, code_type: CodeType) -> int:
        """计算缩进级别 - 针对shader语言优化"""
        line = line.strip()
        
        # 减少缩进的情况（优先级更高）
        if line.startswith('}'):
            return max(0, current_level - 1)
        elif line.startswith('else') and not line.startswith('else if'):
            return max(0, current_level - 1)
        elif line.startswith('case ') or line.startswith('default:'):
            return max(0, current_level - 1)
        
        # 增加缩进的情况
        if line.endswith('{'):
            return current_level + 1
        elif any(line.startswith(keyword) for keyword in ['if', 'for', 'while', 'do', 'switch']):
            # 检查是否有大括号在同一行
            if '{' in line:
                return current_level + 1
        elif line.startswith('struct ') or line.startswith('cbuffer '):
            # struct和cbuffer定义后增加缩进
            if '{' in line:
                return current_level + 1
        
        return current_level

# 全局shader格式化器实例
_shader_formatter = ShaderFormatter()

def format_shader_code(code: str, indent_size: int = 4) -> str:
    """格式化shader代码的全局函数"""
    return _shader_formatter.format_shader_code(code, indent_size)

def is_clang_format_available() -> bool:
    """检查clang-format是否可用"""
    return _shader_formatter.check_clang_format_available()
