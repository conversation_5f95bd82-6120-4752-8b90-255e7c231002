# Process UI组件说明

本文档说明了为Process模块创建的UI组件，这些组件按功能分组，每个组对应一个文件夹。

## 组件结构

### 1. WidgetBasicOptimizers - 基础优化器组
**文件**: `widget_basic_optimizers.py`
**对应**: `Process/basic_optimizers/` 文件夹

包含8个专业优化器：
- 纹理采样优化器
- 循环展开优化器  
- 分支优化器
- 内存访问优化器
- 数学函数优化器
- 向量化优化器
- 精度分析优化器
- 寄存器分配优化器

**参数**:
- 优化器选择下拉框
- 优化级别选择（基础/激进/最大）
- 动态功能说明标签

### 2. WidgetExternalTools - 外部工具优化器组
**文件**: `widget_external_tools.py`
**对应**: `Process/external_tools/` 文件夹

包含依赖外部工具的优化器：
- Clang HLSL优化器
- Tree-sitter HLSL优化器
- SPIR-V工具优化器

**特性**:
- 自动检测依赖可用性
- 根据可用性动态显示选项
- 每个工具有专门的参数设置
- 实时状态显示

**Clang参数**:
- 优化级别（O0-O3）
- 启用警告
- 启用静态分析

**Tree-sitter参数**:
- 分析类型（语法树/语义/结构/完整）
- 启用代码重构
- 启用代码格式化
- 启用语法验证

**SPIR-V参数**:
- 优化级别（基础/性能/大小/最大）
- 优化轮数（1-10）
- 验证SPIR-V
- 保留调试信息

### 3. WidgetLegacyOptimizers - 传统优化器组
**文件**: `widget_legacy_optimizers.py`
**对应**: `Process/legacy_optimizers/` 文件夹

包含项目原有的优化器：
- 通用自动优化器
- Half/Float类型转换器

**Half/Float转换器参数**:
- 转换方向（half→float / float→half）
- 转换方式（全局/局部变量/全局变量/函数参数/函数返回值/带注释）

## 设计特点

### 1. 统一的基类
所有组件都继承自 `ProcessWidgetBase`，提供：
- 统一的界面风格
- 自动获取源代码功能
- 统一的结果显示机制

### 2. 动态参数界面
- 根据选择的优化器动态显示参数
- 智能的依赖检查和状态显示
- 清晰的功能说明

### 3. 智能依赖管理
外部工具组件会：
- 自动检测系统依赖
- 只显示可用的优化器
- 提供清晰的安装指导

### 4. 用户友好的界面
- 清晰的分组和标签
- 详细的功能说明
- 实时的状态反馈

## 注册方式

在 `ui/process_widget.py` 中注册：

```python
self.algorithms = {
    # 传统优化器（保持向后兼容）
    'float_half_convert': WidgetHalfFloatConvert(),
    
    # 新的分组优化器
    'basic_optimizers': WidgetBasicOptimizers(),
    'external_tools': WidgetExternalTools(),
    'legacy_optimizers': WidgetLegacyOptimizers(),
}
```

## 使用方式

### 在主程序中调用
```python
# 显示基础优化器
process_widget.show_algorithm('basic_optimizers')

# 显示外部工具
process_widget.show_algorithm('external_tools')

# 显示传统优化器
process_widget.show_algorithm('legacy_optimizers')
```

### 测试组件
运行测试脚本：
```bash
cd ui/Process
python test_widgets.py
```

## 扩展指南

### 添加新的优化器到现有组
1. 在对应的widget文件中更新 `optimizer_combo` 选项
2. 在 `optimizer_map` 中添加映射
3. 在 `info_map` 中添加说明
4. 如需要额外参数，在 `on_optimizer_changed` 中添加参数设置

### 添加新的优化器组
1. 创建新的widget文件，继承 `ProcessWidgetBase`
2. 实现优化器选择和参数设置
3. 在 `process_widget.py` 中注册
4. 更新文档

### 添加新的参数类型
在对应的 `setup_xxx_params` 方法中添加：
- QSpinBox: 数值输入
- QCheckBox: 布尔选项
- QComboBox: 选择列表
- QLineEdit: 文本输入
- QSlider: 滑块选择

## 注意事项

1. **依赖检查**: 外部工具组件会自动检查依赖，确保用户体验
2. **错误处理**: 所有组件都有完善的错误处理机制
3. **向后兼容**: 保留了原有的 `WidgetHalfFloatConvert` 以确保兼容性
4. **性能考虑**: 参数界面采用动态创建，避免不必要的资源占用

这种设计使得UI组件既模块化又易于维护，同时为用户提供了清晰直观的操作界面。
