from PyQt5.QtWidgets import QWidget, QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QKeySequence
import os
from typing import List

class DisplayBaseWidget(QWidget):
    LANG_MAP = {
        'hlsl': 'hlsl',
        'glsl': 'glsl',
        'shader': 'hlsl',
        'cpp': 'cpp',
        'c++': 'cpp',
        'python': 'python',
        'javascript': 'javascript',
        'js': 'javascript',
    }

    # 信号：当文件保存时发出
    file_saved = pyqtSignal(str)  # 发出保存的文件路径
    # 信号：当修改状态变化时发出
    modified_changed = pyqtSignal(bool)  # 发出修改状态

    def __init__(self, parent=None, model='code', source=None):
        super().__init__(parent)
        self.model = model  # 记录显示模式
        self.source = source  # 记录文本来源（如文件路径、url等）
        self.is_modified = False  # 记录是否已修改
        
        # 历史记录用于撤销/重做功能
        self.history: List[str] = []
        self.history_index = -1
        self.max_history = 50  # 最大历史记录数量
        # 初始化历史记录
        self.add_to_history("")

    def set_text(self, text):
        raise NotImplementedError

    def clear_text(self):
        raise NotImplementedError

    def set_label(self, label):
        raise NotImplementedError

    def set_language(self, lang):
        """返回标准化后的语言名，供子类调用"""
        return self.LANG_MAP.get(lang.lower(), 'text')

    def get_text(self):
        raise NotImplementedError

    def set_editable(self, editable=True):
        """设置是否可编辑，子类需要重写"""
        raise NotImplementedError

    def save_file(self):
        """保存文件，支持Ctrl+S快捷键"""
        if self.source and os.path.isfile(self.source):
            # 如果source是有效的文件路径，直接保存
            try:
                content = self.get_text()
                # 保存文件时不添加额外的换行符
                with open(self.source, 'w', encoding='utf-8', newline='') as f:
                    f.write(content)
                self.set_modified(False)  # 使用set_modified来触发信号
                self.file_saved.emit(self.source)
                QMessageBox.information(self, '保存成功', f'文件已保存到:\n{self.source}')
                return True
            except Exception as e:
                QMessageBox.warning(self, '保存失败', f'保存文件失败:\n{self.source}\n{e}')
                return False
        else:
            # 如果source不是有效路径，弹出另存为对话框
            return self.save_as_file()

    def save_as_file(self):
        """另存为文件"""
        # 设置文件过滤器
        file_filters = (
            'HLSL文件 (*.hlsl);;'
            'GLSL文件 (*.glsl *.vert *.frag *.geom *.tesc *.tese *.comp);;'
            'Shader文件 (*.shader);;'
            'C++文件 (*.cpp *.h *.hpp);;'
            '文本文件 (*.txt);;'
            '所有文件 (*)'
        )

        # 根据当前source设置默认文件名
        default_name = 'untitled.hlsl'
        if self.source:
            if os.path.isfile(self.source):
                default_name = os.path.basename(self.source)
            else:
                default_name = f'{self.source}'
                if not default_name.endswith(('.hlsl', '.glsl', '.shader')):
                    default_name += '.hlsl'

        file_path, _ = QFileDialog.getSaveFileName(self, '另存为', default_name, file_filters)

        if file_path:
            try:
                content = self.get_text()
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(content)
                self.source = file_path
                self.set_modified(False)
                self.file_saved.emit(file_path)
                QMessageBox.information(self, '保存成功', f'文件已保存到:\n{file_path}')
                return True
            except Exception as e:
                QMessageBox.warning(self, '保存失败', f'保存文件失败:\n{file_path}\n{e}')
                return False
        return False

    def set_modified(self, modified=True):
        """设置修改状态"""
        if self.is_modified != modified:
            self.is_modified = modified
            self.modified_changed.emit(modified)
    
    def add_to_history(self, text: str = None):
        """将当前文本添加到历史记录"""
        if text is None:
            text = self.get_text()
        
        # 如果与当前历史记录相同，则不添加
        if self.history and self.history_index >= 0 and text == self.history[self.history_index]:
            return
        
        # 移除当前位置之后的所有历史记录
        if self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]
        
        # 添加新的历史记录
        self.history.append(text)
        self.history_index = len(self.history) - 1
        
        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history.pop(0)
            self.history_index -= 1
    
    def undo(self):
        """撤销操作 - 自动检测并使用内置undo功能"""
        # 尝试使用内置undo功能
        if hasattr(self, 'text'):
            # QScintilla组件
            if hasattr(self.text, 'isUndoAvailable') and self.text.isUndoAvailable():
                self.text.undo()
                return True
            # QTextEdit组件
            elif hasattr(self.text, 'document') and hasattr(self.text.document(), 'isUndoAvailable'):
                if self.text.document().isUndoAvailable():
                    self.text.undo()
                    return True
        
        # 如果内置功能不可用，使用历史记录实现
        if self.history_index > 0:
            self.history_index -= 1
            self.set_text(self.history[self.history_index])
            return True
        return False
    
    def redo(self):
        """重做操作 - 自动检测并使用内置redo功能"""
        # 尝试使用内置redo功能
        if hasattr(self, 'text'):
            # QScintilla组件
            if hasattr(self.text, 'isRedoAvailable') and self.text.isRedoAvailable():
                self.text.redo()
                return True
            # QTextEdit组件
            elif hasattr(self.text, 'document') and hasattr(self.text.document(), 'isRedoAvailable'):
                if self.text.document().isRedoAvailable():
                    self.text.redo()
                    return True
        
        # 如果内置功能不可用，使用历史记录实现
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.set_text(self.history[self.history_index])
            return True
        return False
    
    def can_undo(self) -> bool:
        """检查是否可以撤销 - 自动检测"""
        # 尝试使用内置功能
        if hasattr(self, 'text'):
            # QScintilla组件
            if hasattr(self.text, 'isUndoAvailable'):
                return self.text.isUndoAvailable()
            # QTextEdit组件
            elif hasattr(self.text, 'document') and hasattr(self.text.document(), 'isUndoAvailable'):
                return self.text.document().isUndoAvailable()
        
        # 使用历史记录实现
        return self.history_index > 0
    
    def can_redo(self) -> bool:
        """检查是否可以重做 - 自动检测"""
        # 尝试使用内置功能
        if hasattr(self, 'text'):
            # QScintilla组件
            if hasattr(self.text, 'isRedoAvailable'):
                return self.text.isRedoAvailable()
            # QTextEdit组件
            elif hasattr(self.text, 'document') and hasattr(self.text.document(), 'isRedoAvailable'):
                return self.text.document().isRedoAvailable()
        
        # 使用历史记录实现
        return self.history_index < len(self.history) - 1
    
    def format_code(self):
        """格式化代码 - 统一实现，所有子类共享"""
        try:
            # 优先使用专门的shader格式化器
            from ui.code_formatter import format_shader_code, is_clang_format_available
            
            current_text = self.get_text()
            if current_text.strip():
                # 使用专门的shader格式化器
                formatted_text = format_shader_code(current_text)
                if formatted_text != current_text:
                    self.set_text(formatted_text)
                    self.set_modified(True)
                    # 移除弹窗提示，静默格式化
                    return
                # 如果代码没有变化，也不显示弹窗
                return
            # 如果没有可格式化的内容，也不显示弹窗
            return
                
        except Exception as e:
            # 只在出错时显示错误信息
            QMessageBox.warning(self, '格式化失败', f'代码格式化失败:\n{str(e)}')