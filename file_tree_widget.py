#!/usr/bin/env python3
"""
文件树组件，类似VSCode的文件浏览器
"""
import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem,
                             QHeaderView, QMenu, QAction, QMessageBox, QPushButton, QLabel, QHBoxLayout, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPainter, QPalette, QBrush, QColor

class CustomTreeWidget(QTreeWidget):
    """自定义QTreeWidget，支持整行高亮"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # 使用更强制的样式来确保整行高亮
        self.setStyleSheet("""
            QTreeWidget::item {
                border: none;
                padding: 2px;
            }
            QTreeWidget::item:hover {
                background-color: #e8e8e8;
                border: none;
                outline: none;
            }
            QTreeWidget::item:selected {
                background-color: #0078d4;
                color: white;
                border: none;
                outline: none;
            }
            QTreeWidget::item:selected:hover {
                background-color: #106ebe;
                color: white;
                border: none;
                outline: none;
            }
        """)

class FileTreeWidget(QWidget):
    # 信号：当文件被双击时发出
    file_double_clicked = pyqtSignal(str)  # 发出文件路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.root_paths = []  # 存储根目录路径
        self.file_items = {}  # 文件路径到TreeWidgetItem的映射
        self.temp_files_root = None  # 临时文件根节点
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(0)

        # 添加标题栏
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 5)

        title_label = QLabel("文件浏览器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 9pt;
                color: #333;
                padding: 2px 0px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 添加刷新按钮
        refresh_btn = QPushButton("⟳")
        refresh_btn.setFixedSize(20, 20)
        refresh_btn.setToolTip("刷新文件树")
        refresh_btn.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
                font-size: 12px;
                color: #666;
                border-radius: 3px;
            }
            QPushButton:hover {
                background: #e0e0e0;
                color: #333;
            }
            QPushButton:pressed {
                background: #d0d0d0;
            }
        """)
        refresh_btn.clicked.connect(self.refresh)
        title_layout.addWidget(refresh_btn)

        layout.addLayout(title_layout)

        # 创建空状态提示
        self.empty_label = QLabel("暂无文件\n\n点击菜单中的\n'导入文件' 或 '导入文件夹'\n来开始使用")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("""
            QLabel {
                color: #999;
                font-size: 10px;
                padding: 20px;
                background: transparent;
            }
        """)

        # 创建树形控件
        self.tree = CustomTreeWidget()
        self.tree.setHeaderHidden(True)  # 隐藏表头
        self.tree.setRootIsDecorated(False)  # 隐藏默认的展开/折叠图标
        self.tree.setIndentation(20)  # 设置缩进
        self.tree.setUniformRowHeights(True)  # 统一行高
        self.tree.setAllColumnsShowFocus(True)  # 整行选中
        self.tree.setItemsExpandable(True)  # 允许展开
        self.tree.setExpandsOnDoubleClick(False)  # 禁用双击展开，我们自己处理

        # 设置额外属性确保整行高亮
        self.tree.setSelectionBehavior(self.tree.SelectRows)  # 选择整行
        self.tree.setMouseTracking(True)  # 启用鼠标跟踪
        self.tree.setAllColumnsShowFocus(True)  # 确保整行显示焦点
        self.tree.setAlternatingRowColors(False)  # 禁用交替行颜色
        self.tree.setFrameStyle(0)  # 移除边框

        # 设置现代化样式 - 强制整行高亮
        self.tree.setStyleSheet("""
            QTreeWidget {
                background-color: #ffffff;
                border: none;
                outline: none;
                font-size: 9pt;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                show-decoration-selected: 1;
            }
            QTreeWidget::item {
                height: 22px;
                padding: 2px 4px;
                border: none;
                color: #333;
                margin: 0px;
                background: transparent;
            }
            QTreeWidget::item:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e8e8e8, stop: 0.99 #e8e8e8, stop: 1 #e8e8e8);
                border: none;
                margin: 0px;
            }
            QTreeWidget::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #0078d4, stop: 0.99 #0078d4, stop: 1 #0078d4);
                color: white;
                border: none;
                margin: 0px;
            }
            QTreeWidget::item:selected:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #106ebe, stop: 0.99 #106ebe, stop: 1 #106ebe);
                color: white;
                border: none;
                margin: 0px;
            }
            QTreeWidget::item:selected:!active {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #cccccc, stop: 0.99 #cccccc, stop: 1 #cccccc);
                color: #333;
                border: none;
                margin: 0px;
            }
            QTreeWidget::branch {
                background: transparent;
                width: 0px;
                border: none;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                background: transparent;
                border: none;
                width: 0px;
                height: 0px;
                image: none;
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                background: transparent;
                border: none;
                width: 0px;
                height: 0px;
                image: none;
            }
            QScrollBar:vertical {
                background: #f0f0f0;
                width: 8px;
                border: none;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # 连接信号
        self.tree.itemClicked.connect(self._on_item_clicked)
        self.tree.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self._show_context_menu)

        # 连接展开/折叠信号来更新图标
        self.tree.itemExpanded.connect(self._on_item_expanded)
        self.tree.itemCollapsed.connect(self._on_item_collapsed)

        layout.addWidget(self.empty_label)
        layout.addWidget(self.tree)

        # 初始状态显示空提示
        self._update_empty_state()
        
    def add_root_path(self, path):
        """添加根目录"""
        if not os.path.exists(path):
            return False
            
        # 避免重复添加
        abs_path = os.path.abspath(path)
        if abs_path in self.root_paths:
            return True
            
        self.root_paths.append(abs_path)

        if os.path.isfile(abs_path):
            # 如果是单个文件，添加到临时文件文件夹
            self._add_file_to_temp_folder(abs_path)
            # 选中该文件
            self._select_file(abs_path)
        else:
            # 如果是目录，直接添加（作为一级目录）
            self._add_directory_to_tree(abs_path, None, is_root_level=True)

        # 更新空状态
        self._update_empty_state()
        return True

    def _ensure_temp_folder_exists(self):
        """确保临时文件文件夹存在"""
        if self.temp_files_root is None:
            # 创建临时文件根节点，插入到最顶部
            self.temp_files_root = QTreeWidgetItem(self.tree)
            self.temp_files_root.setText(0, "📁 临时文件")
            self.temp_files_root.setData(0, Qt.UserRole, "")
            self.temp_files_root.setData(0, Qt.UserRole + 1, 'temp_folder')
            
            # 设置临时文件夹样式（作为一级目录）
            self.temp_files_root.setForeground(0, QBrush(QColor("#1e3a8a")))  # 深蓝色，与一级目录一致
            font = self.temp_files_root.font(0)
            font.setBold(True)
            font.setPointSize(font.pointSize() + 1)  # 稍大字体
            self.temp_files_root.setFont(0, font)
            
            # 设置背景色，创建分割条效果
            self.temp_files_root.setBackground(0, QBrush(QColor("#f3f4f6")))  # 浅灰色背景
            
            # 设置工具提示
            self.temp_files_root.setToolTip(0, "临时文件根目录")
            
            # 默认展开
            self.temp_files_root.setExpanded(True)
            
            # 将临时文件夹移动到最顶部
            self._move_temp_folder_to_top()

    def _move_temp_folder_to_top(self):
        """将临时文件夹移动到文件树的最顶部"""
        if self.temp_files_root and self.tree.indexOfTopLevelItem(self.temp_files_root) > 0:
            # 获取当前临时文件夹的索引
            current_index = self.tree.indexOfTopLevelItem(self.temp_files_root)
            # 移除临时文件夹
            self.tree.takeTopLevelItem(current_index)
            # 重新插入到索引0的位置（最顶部）
            self.tree.insertTopLevelItem(0, self.temp_files_root)

    def _add_file_to_temp_folder(self, file_path):
        """将单个文件添加到临时文件文件夹"""
        # 确保临时文件夹存在
        self._ensure_temp_folder_exists()
        
        file_name = os.path.basename(file_path)
        
        # 创建文件项作为临时文件夹的子项
        file_item = QTreeWidgetItem(self.temp_files_root)
        file_item.setText(0, f"📄 {file_name}")
        file_item.setData(0, Qt.UserRole, file_path)
        file_item.setData(0, Qt.UserRole + 1, 'file')
        
        # 根据文件扩展名设置颜色
        ext = os.path.splitext(file_name)[1].lower()
        color = self._get_file_color(ext)
        if color:
            file_item.setForeground(0, QBrush(QColor(color)))
        
        # 设置工具提示显示完整路径
        file_item.setToolTip(0, f"完整路径: {file_path}")
        
        # 将文件项映射存储
        self.file_items[file_path] = file_item
        
    def _add_directory_to_tree(self, dir_path, parent_item, is_root_level=False):
        """递归添加目录到树中"""
        try:
            dir_name = os.path.basename(dir_path) or dir_path

            # 创建目录项
            if parent_item is None:
                dir_item = QTreeWidgetItem(self.tree)
            else:
                dir_item = QTreeWidgetItem(parent_item)

            # 设置文件夹显示名称，添加展开图标
            dir_item.setText(0, f"📁 {dir_name}")  # 使用文件夹emoji图标，更清晰
            dir_item.setData(0, Qt.UserRole, dir_path)
            dir_item.setData(0, Qt.UserRole + 1, 'directory')

            # 根据是否为一级目录设置不同样式
            if is_root_level:
                # 一级目录样式：深蓝色，更大字体，粗体
                dir_item.setForeground(0, QBrush(QColor("#1e3a8a")))  # 深蓝色
                font = dir_item.font(0)
                font.setBold(True)
                font.setPointSize(font.pointSize() + 1)  # 稍大字体
                dir_item.setFont(0, font)
                
                # 设置背景色，创建分割条效果
                dir_item.setBackground(0, QBrush(QColor("#f3f4f6")))  # 浅灰色背景
                
                # 设置工具提示
                dir_item.setToolTip(0, f"根目录: {dir_path}")
            else:
                # 次级目录样式：深灰色，粗体
                dir_item.setForeground(0, QBrush(QColor("#374151")))  # 深灰色
                font = dir_item.font(0)
                font.setBold(True)
                dir_item.setFont(0, font)

            # 设置文件夹的文本对齐
            dir_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)
            
            # 获取目录内容
            try:
                items = os.listdir(dir_path)
                items.sort()  # 排序
                
                # 先添加目录，再添加文件
                directories = []
                files = []
                
                for item in items:
                    item_path = os.path.join(dir_path, item)
                    if os.path.isdir(item_path):
                        directories.append((item, item_path))
                    else:
                        files.append((item, item_path))
                
                # 添加子目录
                for dir_name, dir_path in directories:
                    self._add_directory_to_tree(dir_path, dir_item)
                
                # 添加文件
                for file_name, file_path in files:
                    self._add_file_to_tree(file_name, file_path, dir_item)
                    
            except PermissionError:
                # 没有权限访问的目录
                pass
                
        except Exception as e:
            print(f"添加目录到树失败: {e}")
            
    def _add_file_to_tree(self, file_name, file_path, parent_item):
        """添加文件到树中"""
        file_item = QTreeWidgetItem(parent_item)

        # 文件显示，添加📄标记
        file_item.setText(0, f"📄 {file_name}")
        file_item.setData(0, Qt.UserRole, file_path)
        file_item.setData(0, Qt.UserRole + 1, 'file')

        # 根据文件扩展名设置颜色
        ext = os.path.splitext(file_name)[1].lower()
        color = self._get_file_color(ext)
        if color:
            file_item.setForeground(0, QBrush(QColor(color)))

        # 存储文件项映射
        self.file_items[file_path] = file_item

    def _get_file_color(self, ext):
        """根据文件扩展名获取颜色"""
        # 简化的颜色方案，只区分几种主要文件类型
        if ext in ['.hlsl', '.glsl', '.shader', '.vert', '.frag', '.geom', '.comp']:
            return '#0078d4'  # Shader文件 - 蓝色
        elif ext in ['.cpp', '.h', '.hpp', '.c']:
            return '#666666'  # C++文件 - 深灰色
        else:
            return None  # 其他文件使用默认颜色
            
    def _select_file(self, file_path):
        """选中指定文件"""
        if file_path in self.file_items:
            item = self.file_items[file_path]
            self.tree.setCurrentItem(item)
            self.tree.scrollToItem(item)
            
    def _on_item_clicked(self, item, column):
        """处理项目单击事件"""
        item_type = item.data(0, Qt.UserRole + 1)
        item_path = item.data(0, Qt.UserRole)

        if item_type == 'directory':
            # 单击目录，展开/折叠
            item.setExpanded(not item.isExpanded())
        elif item_type == 'temp_folder':
            # 单击临时文件夹，展开/折叠
            item.setExpanded(not item.isExpanded())
        elif item_type == 'file':
            # 单击文件，发出信号
            self.file_double_clicked.emit(item_path)

    def _on_item_double_clicked(self, item, column):
        """处理项目双击事件"""
        item_type = item.data(0, Qt.UserRole + 1)
        item_path = item.data(0, Qt.UserRole)

        if item_type == 'file':
            # 双击文件，也发出信号（与单击行为一致）
            self.file_double_clicked.emit(item_path)
        elif item_type == 'temp_folder':
            # 双击临时文件夹，也展开/折叠（与单击行为一致）
            item.setExpanded(not item.isExpanded())
        elif item_type == 'directory':
            # 双击目录，也展开/折叠（与单击行为一致）
            item.setExpanded(not item.isExpanded())
            
    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self.tree.itemAt(position)
        if item is None:
            return
            
        menu = QMenu(self)
        
        item_type = item.data(0, Qt.UserRole + 1)
        item_path = item.data(0, Qt.UserRole)
        
        if item_type == 'file':
            # 文件右键菜单
            open_action = QAction('打开', self)
            open_action.triggered.connect(lambda: self.file_double_clicked.emit(item_path))
            menu.addAction(open_action)
            
            menu.addSeparator()
            
            copy_path_action = QAction('复制路径', self)
            copy_path_action.triggered.connect(lambda: self._copy_path_to_clipboard(item_path))
            menu.addAction(copy_path_action)
            
        elif item_type == 'temp_folder':
            # 临时文件夹右键菜单
            expand_action = QAction('展开', self)
            expand_action.triggered.connect(lambda: item.setExpanded(True))
            menu.addAction(expand_action)
            
            collapse_action = QAction('折叠', self)
            collapse_action.triggered.connect(lambda: item.setExpanded(False))
            menu.addAction(collapse_action)
            
        elif item_type == 'directory':
            # 目录右键菜单
            expand_action = QAction('展开', self)
            expand_action.triggered.connect(lambda: item.setExpanded(True))
            menu.addAction(expand_action)
            
            collapse_action = QAction('折叠', self)
            collapse_action.triggered.connect(lambda: item.setExpanded(False))
            menu.addAction(collapse_action)
            
            menu.addSeparator()
            
            copy_path_action = QAction('复制路径', self)
            copy_path_action.triggered.connect(lambda: self._copy_path_to_clipboard(item_path))
            menu.addAction(copy_path_action)
            
        menu.exec_(self.tree.mapToGlobal(position))
        
    def _copy_path_to_clipboard(self, path):
        """复制路径到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(path)
        
    def refresh(self):
        """刷新文件树"""
        self.tree.clear()
        self.file_items.clear()
        self.temp_files_root = None  # 重置临时文件夹根节点
        
        for root_path in self.root_paths:
            if os.path.isfile(root_path):
                # 使用新的临时文件夹处理方法
                self._add_file_to_temp_folder(root_path)
                self._select_file(root_path)
            else:
                self._add_directory_to_tree(root_path, None, is_root_level=True)
        
        # 确保临时文件夹在最顶部
        if self.temp_files_root:
            self._move_temp_folder_to_top()
                
    def clear(self):
        """清空文件树"""
        self.tree.clear()
        self.file_items.clear()
        self.root_paths.clear()
        self._update_empty_state()

    def _update_empty_state(self):
        """更新空状态显示"""
        has_items = self.tree.topLevelItemCount() > 0
        self.empty_label.setVisible(not has_items)
        self.tree.setVisible(has_items)

    def _on_item_expanded(self, item):
        """处理项目展开事件"""
        if item.data(0, Qt.UserRole + 1) == 'directory':
            text = item.text(0)
            if text.startswith('📁 '):
                dir_name = text[2:]  # 移除文件夹图标和空格
                item.setText(0, f"📂 {dir_name}")  # 使用打开的文件夹图标

    def _on_item_collapsed(self, item):
        """处理项目折叠事件"""
        if item.data(0, Qt.UserRole + 1) == 'directory':
            text = item.text(0)
            if text.startswith('📂 '):
                dir_name = text[2:]  # 移除打开文件夹图标和空格
                item.setText(0, f"📁 {dir_name}")  # 使用关闭的文件夹图标


        
    def get_selected_file(self):
        """获取当前选中的文件路径"""
        current_item = self.tree.currentItem()
        if current_item:
            item_type = current_item.data(0, Qt.UserRole + 1)
            if item_type == 'file':
                return current_item.data(0, Qt.UserRole)
        return None
