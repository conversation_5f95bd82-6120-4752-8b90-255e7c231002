from PyQt5.QtWidgets import QVBoxLayout, QLabel, QTextEdit, QScrollBar, QShortcut
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QKeySequence
from .text_display_widget_base import DisplayBaseWidget

class TextDisplayWidget(DisplayBaseWidget):
    def __init__(self, parent=None, model='Original', source=None):
        super().__init__(parent, model=model, source=source)
        layout = QVBoxLayout(self)
        self.text = QTextEdit()
        self.text.setReadOnly(False)  # 默认设置为可编辑
        self.text.setLineWrapMode(QTextEdit.NoWrap)
        self.text.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.text.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.text)
        self.setLayout(layout)

        # 设置快捷键
        self._setup_shortcuts()

        # 连接文本变化信号
        self.text.textChanged.connect(self._on_text_changed)

    def _setup_shortcuts(self):
        """设置快捷键"""
        # 不在这里设置快捷键，避免冲突
        # 快捷键将由主窗口统一管理
        pass

    def _on_text_changed(self):
        """文本变化时的处理"""
        if not self.is_modified:  # 只有在未修改状态时才设置为已修改
            self.set_modified(True)

    def set_text(self, text):
        self.text.setPlainText(text)
        self.set_modified(False)

    def clear_text(self):
        self.text.clear()
        self.set_modified(False)

    def set_label(self, label):
        pass

    def get_text(self):
        return self.text.toPlainText()

    def set_editable(self, editable=True):
        """设置是否可编辑"""
        self.text.setReadOnly(not editable)