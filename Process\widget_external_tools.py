from PyQt5.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QCheckBox, QSpinBox
from ui.Process.process_widget_base import ProcessWidgetBase
from Process.external_tools import check_dependencies, get_available_optimizers

class WidgetExternalTools(ProcessWidgetBase):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.label.setText('外部工具优化器')
        # 功能说明映射
        self.info_map = {
            'clang': 'Clang HLSL优化器\n• 编译器级别优化\n• 静态代码分析\n• 语法检查和警告\n• 高级代码转换\n\n需要：libclang',
            'tree_sitter': 'Tree-sitter HLSL优化器\n• 精确语法树解析\n• 代码结构分析\n• 智能重构建议\n• 语法错误检测\n\n需要：tree-sitter + HLSL语法库',
            'spirv_tools': 'SPIR-V工具优化器\n• SPIR-V字节码优化\n• 跨平台兼容性\n• 高级优化passes\n• 验证和调试\n\n需要：SPIR-V工具链',
            'none': '没有可用的外部工具优化器\n请安装以下工具之一：\n• libclang\n• tree-sitter\n• SPIR-V工具链'
        }
        # 检查依赖
        self.deps = check_dependencies()
        self.available_optimizers = get_available_optimizers()
        
        # 优化器选择
        optimizer_layout = QHBoxLayout()
        optimizer_label = QLabel('优化器:')
        self.optimizer_combo = QComboBox()

        # 添加所有选项，但标记可用性状态
        clang_available = 'ClangHLSLOptimizer' in self.available_optimizers
        tree_sitter_available = 'TreeSitterHLSLOptimizer' in self.available_optimizers
        spirv_available = 'SPIRVToolsHLSLOptimizer' in self.available_optimizers

        clang_text = 'Clang HLSL优化器' + (' ✅' if clang_available else ' ❌ 未安装')
        tree_sitter_text = 'Tree-sitter HLSL优化器' + (' ✅' if tree_sitter_available else ' ❌ 未安装')
        spirv_text = 'SPIR-V工具优化器' + (' ✅' if spirv_available else ' ❌ 未安装')

        self.optimizer_combo.addItem(clang_text, 'clang')
        self.optimizer_combo.addItem(tree_sitter_text, 'tree_sitter')
        self.optimizer_combo.addItem(spirv_text, 'spirv_tools')

        # 默认选择第一个可用的优化器
        if clang_available:
            self.optimizer_combo.setCurrentIndex(0)
        elif tree_sitter_available:
            self.optimizer_combo.setCurrentIndex(1)
        elif spirv_available:
            self.optimizer_combo.setCurrentIndex(2)
            
        self.optimizer_combo.currentTextChanged.connect(self.on_optimizer_changed)
        optimizer_layout.addWidget(optimizer_label)
        optimizer_layout.addWidget(self.optimizer_combo)
        
        # 参数容器
        self.params_layout = QVBoxLayout()
        
        # 功能说明标签
        self.info_label = QLabel()
        self.info_label.setStyleSheet('color: gray; font-size: 10px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;')
        self.info_label.setWordWrap(True)
        
        # 插入到主布局
        self.layout.insertLayout(1, optimizer_layout)
        self.layout.insertLayout(2, self.params_layout)
        self.layout.insertWidget(3, self.info_label)
        
        # 初始化显示
        self.on_optimizer_changed()
        


    def clear_params_layout(self):
        """清空参数布局"""
        def clear_layout(layout):
            while layout.count():
                child = layout.takeAt(0)
                if child is None:
                    continue

                widget = child.widget()
                if widget is not None:
                    widget.setParent(None)
                    widget.deleteLater()

                child_layout = child.layout()
                if child_layout is not None:
                    clear_layout(child_layout)
                    child_layout.deleteLater()

        clear_layout(self.params_layout)

        # 强制处理待删除的对象
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

    def setup_clang_params(self):
        """设置Clang参数"""
        # 优化级别
        level_layout = QHBoxLayout()
        level_label = QLabel('优化级别:')
        self.clang_level_combo = QComboBox()
        self.clang_level_combo.addItem('O0 - 无优化', '-O0')
        self.clang_level_combo.addItem('O1 - 基础优化', '-O1')
        self.clang_level_combo.addItem('O2 - 标准优化', '-O2')
        self.clang_level_combo.addItem('O3 - 激进优化', '-O3')
        self.clang_level_combo.setCurrentIndex(2)
        level_layout.addWidget(level_label)
        level_layout.addWidget(self.clang_level_combo)
        
        # 选项
        self.clang_warnings = QCheckBox('启用警告')
        self.clang_warnings.setChecked(True)
        self.clang_analysis = QCheckBox('启用静态分析')
        
        self.params_layout.addLayout(level_layout)
        self.params_layout.addWidget(self.clang_warnings)
        self.params_layout.addWidget(self.clang_analysis)

    def setup_tree_sitter_params(self):
        """设置Tree-sitter参数"""
        # 分析类型
        analysis_layout = QHBoxLayout()
        analysis_label = QLabel('分析类型:')
        self.ts_analysis_combo = QComboBox()
        self.ts_analysis_combo.addItem('语法树分析', 'syntax_tree')
        self.ts_analysis_combo.addItem('语义分析', 'semantic')
        self.ts_analysis_combo.addItem('结构优化', 'structure')
        self.ts_analysis_combo.addItem('完整分析', 'full')
        analysis_layout.addWidget(analysis_label)
        analysis_layout.addWidget(self.ts_analysis_combo)
        
        # 选项
        self.ts_refactoring = QCheckBox('启用代码重构')
        self.ts_refactoring.setChecked(True)
        self.ts_formatting = QCheckBox('启用代码格式化')
        self.ts_validation = QCheckBox('启用语法验证')
        self.ts_validation.setChecked(True)
        
        self.params_layout.addLayout(analysis_layout)
        self.params_layout.addWidget(self.ts_refactoring)
        self.params_layout.addWidget(self.ts_formatting)
        self.params_layout.addWidget(self.ts_validation)

    def setup_spirv_params(self):
        """设置SPIR-V参数"""
        # 优化级别
        level_layout = QHBoxLayout()
        level_label = QLabel('优化级别:')
        self.spirv_level_combo = QComboBox()
        self.spirv_level_combo.addItem('基础优化', 'basic')
        self.spirv_level_combo.addItem('性能优化', 'performance')
        self.spirv_level_combo.addItem('大小优化', 'size')
        self.spirv_level_combo.addItem('最大优化', 'max')
        self.spirv_level_combo.setCurrentIndex(1)
        level_layout.addWidget(level_label)
        level_layout.addWidget(self.spirv_level_combo)
        
        # 优化轮数
        rounds_layout = QHBoxLayout()
        rounds_label = QLabel('优化轮数:')
        self.spirv_rounds = QSpinBox()
        self.spirv_rounds.setRange(1, 10)
        self.spirv_rounds.setValue(3)
        rounds_layout.addWidget(rounds_label)
        rounds_layout.addWidget(self.spirv_rounds)
        
        # 选项
        self.spirv_validate = QCheckBox('验证SPIR-V')
        self.spirv_validate.setChecked(True)
        self.spirv_debug = QCheckBox('保留调试信息')
        
        self.params_layout.addLayout(level_layout)
        self.params_layout.addLayout(rounds_layout)
        self.params_layout.addWidget(self.spirv_validate)
        self.params_layout.addWidget(self.spirv_debug)

    def setup_unavailable_info(self, optimizer_key):
        """设置不可用工具的安装提示"""
        from PyQt5.QtWidgets import QLabel, QTextEdit

        install_info = {
            'clang': """安装Clang HLSL优化器：

1. 安装libclang：
   pip install libclang

2. 或者安装完整的LLVM/Clang：
   - Windows: 下载LLVM安装包
   - Linux: sudo apt-get install clang libclang-dev
   - macOS: brew install llvm

3. 确保clang在系统PATH中""",

            'tree_sitter': """安装Tree-sitter HLSL优化器：

1. 安装tree-sitter：
   pip install tree-sitter

2. 下载或编译HLSL语法库：
   - 需要tree-sitter-hlsl.so文件
   - 放置在external_tools目录下

3. 或者使用其他HLSL语法支持库""",

            'spirv_tools': """安装SPIR-V工具优化器：

1. 安装SPIR-V工具链：
   - Windows: 下载Vulkan SDK
   - Linux: sudo apt-get install spirv-tools
   - macOS: brew install spirv-tools

2. 确保spirv-opt在系统PATH中

3. 安装Python绑定（如果需要）：
   pip install spirv-tools"""
        }

        if optimizer_key in install_info:
            info_text = install_info[optimizer_key]
            info_widget = QTextEdit()
            info_widget.setPlainText(info_text)
            info_widget.setReadOnly(True)
            info_widget.setMaximumHeight(150)
            info_widget.setStyleSheet('background-color: #f0f0f0; border: 1px solid #ccc;')
            self.params_layout.addWidget(info_widget)

    def on_optimizer_changed(self):
        """当优化器选择改变时更新参数和说明"""
        self.clear_params_layout()

        optimizer_key = self.optimizer_combo.currentData()
        is_available = False

        # 检查可用性并设置参数界面
        if optimizer_key == 'clang':
            is_available = self.deps.get('clang', False)
            if is_available:
                self.setup_clang_params()
        elif optimizer_key == 'tree_sitter':
            is_available = self.deps.get('tree_sitter', False)
            if is_available:
                self.setup_tree_sitter_params()
        elif optimizer_key == 'spirv_tools':
            is_available = self.deps.get('spirv_tools', False)
            if is_available:
                self.setup_spirv_params()

        # 如果不可用，显示安装提示
        if not is_available:
            self.setup_unavailable_info(optimizer_key)

        # 更新执行按钮状态
        self.btn_run.setEnabled(is_available)

        # 强制刷新布局
        self.params_layout.update()
        self.layout.update()

        # 更新说明
        if optimizer_key in self.info_map:
            info_text = self.info_map[optimizer_key]
            # 添加可用性状态
            status = "✅ 可用" if is_available else "❌ 不可用"
            info_text += f"\n\n状态：{status}"
            self.info_label.setText(info_text)

    def run_algorithm(self, text):
        optimizer_key = self.optimizer_combo.currentData()

        # 检查优化器是否可用
        is_available = self.deps.get({'clang': 'clang', 'tree_sitter': 'tree_sitter', 'spirv_tools': 'spirv_tools'}.get(optimizer_key, ''), False)

        if not is_available:
            return f"错误：{self.optimizer_combo.currentText()} 不可用，请先安装相应的依赖"

        try:
            if optimizer_key == 'clang':
                from Process.external_tools.clang_hlsl_optimizer import ClangHLSLOptimizer
                options = {
                    'optimization_level': self.clang_level_combo.currentData(),
                    'enable_warnings': self.clang_warnings.isChecked(),
                    'enable_analysis': self.clang_analysis.isChecked()
                }
                optimizer = ClangHLSLOptimizer(options)

            elif optimizer_key == 'tree_sitter':
                from Process.external_tools.tree_sitter_hlsl_optimizer import TreeSitterHLSLOptimizer
                options = {
                    'analysis_type': self.ts_analysis_combo.currentData(),
                    'enable_refactoring': self.ts_refactoring.isChecked(),
                    'enable_formatting': self.ts_formatting.isChecked(),
                    'enable_validation': self.ts_validation.isChecked()
                }
                optimizer = TreeSitterHLSLOptimizer(options)

            elif optimizer_key == 'spirv_tools':
                from Process.external_tools.spirvtools_hlsl_optimizer import SPIRVToolsHLSLOptimizer
                options = {
                    'optimization_level': self.spirv_level_combo.currentData(),
                    'optimization_rounds': self.spirv_rounds.value(),
                    'validate_spirv': self.spirv_validate.isChecked(),
                    'preserve_debug': self.spirv_debug.isChecked()
                }
                optimizer = SPIRVToolsHLSLOptimizer(options)

            return optimizer.optimize_code(text)

        except Exception as e:
            return f"优化失败：{str(e)}"
