from PyQt5.QtWidgets import QVBoxLayout, QLabel, QShortcut
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QKeySequence
from PyQt5.Qsci import QsciScintilla, QsciLexerCPP, QsciLexerPython, QsciLexerJavaScript
from .text_display_widget_base import DisplayBaseWidget

class CodeDisplayWidget(DisplayBaseWidget):
    """使用QScintilla的单区域代码显示组件，支持语法高亮"""
    def __init__(self, parent=None, model='QScintilla', source=None):
        super().__init__(parent, model=model, source=source)
        layout = QVBoxLayout(self)
        self.text = QsciScintilla()
        self._setup_editor(self.text)
        layout.addWidget(self.text)
        self.setLayout(layout)
        self.set_language('cpp')

        # 设置快捷键
        self._setup_shortcuts()

        # 连接文本变化信号
        self.text.textChanged.connect(self._on_text_changed)
        
    def _setup_editor(self, editor):
        editor.setReadOnly(False)  # 默认设置为可编辑
        editor.setMarginType(0, QsciScintilla.NumberMargin)
        # 设置行号边距宽度为6位数字，支持显示更多行数
        editor.setMarginWidth(0, "000000")
        editor.setMarginLineNumbers(0, True)
        editor.setMarginsBackgroundColor(Qt.lightGray)
        editor.setCaretLineVisible(True)
        editor.setCaretLineBackgroundColor(Qt.lightGray)
        editor.setSelectionBackgroundColor(Qt.blue)
        editor.setBraceMatching(QsciScintilla.SloppyBraceMatch)
        editor.setIndentationsUseTabs(False)
        editor.setIndentationWidth(4)
        editor.setTabWidth(4)
        editor.setAutoIndent(True)
        try:
            editor.setFolding(QsciScintilla.BoxedTreeFoldStyle)
        except AttributeError:
            try:
                editor.setFolding(QsciScintilla.PlainFoldStyle)
            except AttributeError:
                pass

    def _setup_shortcuts(self):
        """设置快捷键"""
        # 不在这里设置快捷键，避免冲突
        # 快捷键将由主窗口统一管理
        pass

    def _on_save_shortcut(self):
        """Ctrl+S 快捷键被按下"""
        print(f"[SHORTCUT] *** Ctrl+S 快捷键被触发! ***")
        self.save_file()

    def _on_save_as_shortcut(self):
        """Ctrl+Shift+S 快捷键被按下"""
        print(f"[SHORTCUT] *** Ctrl+Shift+S 快捷键被触发! ***")
        self.save_as_file()

    def _on_text_changed(self):
        """文本变化时的处理"""
        if not self.is_modified:  # 只有在未修改状态时才设置为已修改
            self.set_modified(True)
        # 更新行号边距宽度
        self._update_margin_width()
    
    def set_language(self, language):
        std_lang = super().set_language(language)
        lexer = None
        if std_lang in ['cpp', 'hlsl', 'glsl', 'shader']:
            lexer = QsciLexerCPP()
        elif std_lang == 'python':
            lexer = QsciLexerPython()
        elif std_lang == 'javascript':
            lexer = QsciLexerJavaScript()
        if lexer:
            font = lexer.defaultFont(0)
            font.setFamily('Consolas')
            font.setPointSize(10)
            lexer.setDefaultFont(font)
            self.text.setLexer(lexer)
    
    def clear_text(self):
        self.text.clear()
        self.set_modified(False)
        # 更新行号边距宽度
        self._update_margin_width()

    def set_label(self, label):
        pass

    def get_text(self):
        return self.text.text()

    def set_editable(self, editable=True):
        """设置是否可编辑"""
        self.text.setReadOnly(not editable)

    def _update_margin_width(self):
        """动态更新行号边距宽度"""
        line_count = self.text.lines()
        if line_count > 0:
            # 计算需要的位数
            digits = len(str(line_count))
            # 设置边距宽度，至少6位数字，最多8位
            margin_width = "0" * max(6, min(digits + 2, 8))
            self.text.setMarginWidth(0, margin_width)

    def set_text(self, text):
        self.text.setText(text)
        self.set_modified(False)  # 设置文本后重置修改状态
        # 更新行号边距宽度
        self._update_margin_width()
